/* eslint-disable @typescript-eslint/no-explicit-any */
import { Conversation } from "@/app/type";
import { server } from ".";

export const conversation = (
  foodChainId: string,
  message: string,
  onData: any,
  onDish: any,
  func: any,
  outletId: string,
  lastConversation?: Conversation[],
  language?: string
) => {
  const userId = localStorage.getItem("userId");

  // Encode parameters properly to avoid issues with special characters
  const params = new URLSearchParams();
  params.append("foodChainId", foodChainId);
  params.append("message", message);
  params.append("lastConversation", JSON.stringify(lastConversation || []));
  params.append("userId", userId || "");
  params.append("outletId", outletId);

  // Add language parameter if provided
  if (language) {
    params.append("language", language);
  }

  // Create the URL with properly encoded parameters
  const url = `${
    process.env.NEXT_PUBLIC_BASE_URL
  }/api/v1/user/conversation?${params.toString()}`;

  // Add event listener for cart operations
  const eventSource = new EventSource(url);

  eventSource.addEventListener("cartOperation", (event) => {
    const cartOperation = JSON.parse(event.data);
    // Log cart operation for debugging, but don't update conversation here
    // The conversation will be updated in the recommendation event to avoid duplication
    console.log("Cart operation received:", cartOperation.result);
  });

  eventSource.onmessage = (event) => {
    const data = JSON.parse(event.data);

    switch (data.type) {
      case "start":
        onData(data.message);
        break;

      case "recommendation":
        onData(data.data.aiMessage);
        if (data?.data?.recommendations)
          onDish(
            data?.data?.recommendations,
            data?.data?.faqSuggestions,
            data?.data?.cartOperation
          );
        break;

      case "end":
        onData(data.message);
        func();
        eventSource.close();
        break;

      case "error":
        console.error("Error:", data.message);
        eventSource.close();
        break;
    }
  };

  eventSource.onerror = (error) => {
    console.error("EventSource failed:", error);
    // Provide more detailed error information
    onData(
      "Sorry, I'm having trouble connecting to the server. Please try again later."
    );
    func(); // Call the completion function to update UI
    eventSource.close();
  };
};

/**
 * Agentic conversation with intelligent response handling
 */
export const agenticConversation = (
  foodChainId: string,
  message: string,
  onData: any,
  onDish: any,
  func: any,
  outletId: string,
  lastConversation?: Conversation[],
  language?: string
) => {
  const userId = localStorage.getItem("userId");

  // Simplified approach: Remove lastConversation from URL params to avoid 431 error
  // Backend will fetch conversation history from database instead
  const params = new URLSearchParams();
  params.append("foodChainId", foodChainId);
  params.append("message", message);
  params.append("userId", userId || "");
  params.append("outletId", outletId);

  // Add language parameter if provided
  if (language) {
    params.append("language", language);
  }

  // Create the URL with minimal parameters (no conversation history)
  const url = `${
    process.env.NEXT_PUBLIC_BASE_URL
  }/api/v1/user/agentic-conversation?${params.toString()}`;

  // Add event listener for cart operations
  const eventSource = new EventSource(url);

  setupEventSourceHandlers(eventSource, onData, onDish, func);
};

// Helper function to setup EventSource handlers
const setupEventSourceHandlers = (
  eventSource: EventSource,
  onData: any,
  onDish: any,
  func: any
) => {
  let accumulatedMessage = ""; // For handling chunked messages
  let isReceivingChunks = false;
  let expectedTotalChunks = -1;
  let receivedChunks = 0;

  eventSource.addEventListener("cartOperation", (event) => {
    const cartOperation = JSON.parse(event.data);
    console.log("🤖 Agentic cart operation received:", cartOperation.result);
  });

  eventSource.onmessage = (event) => {
    const data = JSON.parse(event.data);

    switch (data.type) {
      case "start":
        onData(data.message);
        break;

      case "recommendation":
        // Handle chunked messages
        if (data.data.isChunk) {
          console.log(`📝 Received chunk ${data.data.chunkIndex + 1}${data.data.totalChunks > 0 ? ` of ${data.data.totalChunks}` : ''}`);

          if (!isReceivingChunks) {
            isReceivingChunks = true;
            accumulatedMessage = "";
            receivedChunks = 0;
          }

          // Accumulate the message
          accumulatedMessage += data.data.aiMessage;
          receivedChunks++;

          // Update expected total chunks if provided
          if (data.data.totalChunks > 0) {
            expectedTotalChunks = data.data.totalChunks;
          }

          // If this is the last chunk or we have all expected chunks
          if (data.data.isLastChunk || (expectedTotalChunks > 0 && receivedChunks >= expectedTotalChunks)) {
            console.log(`✅ Completed chunked message with ${receivedChunks} chunks`);

            // Send the complete accumulated message
            onData(accumulatedMessage);

            // Send recommendations and other data only once (from the first or last chunk)
            if (data?.data?.recommendations && data.data.recommendations.length > 0) {
              onDish(
                data?.data?.recommendations,
                data?.data?.faqSuggestions,
                data?.data?.cartOperation,
                data?.data?.fallbackActions,
                data?.data?.metadata
              );
            }

            // Reset chunking state
            isReceivingChunks = false;
            accumulatedMessage = "";
            expectedTotalChunks = -1;
            receivedChunks = 0;
          }
        } else {
          // Handle normal single message
          if (data.data.aiMessage) {
            onData(data.data.aiMessage);
          }
          if (data?.data?.recommendations) {
            onDish(
              data?.data?.recommendations,
              data?.data?.faqSuggestions,
              data?.data?.cartOperation,
              data?.data?.fallbackActions,
              data?.data?.metadata
            );
          }
        }
        break;

      case "end":
        // Don't add end message if it's empty or just newline to avoid duplication
        if (data.message && data.message.trim() !== "") {
          onData(data.message);
        }
        console.log("✅ AI conversation completed:", {
          type: data.responseType,
          confidence: data.confidence,
          totalItems: data.totalItems,
        });
        func();
        eventSource.close();
        break;

      case "error":
        console.error("❌ Agentic conversation error:", data.message);
        onData(data.message);
        func();
        eventSource.close();
        break;
    }
  };

  eventSource.onerror = (error) => {
    console.error("Agentic EventSource error:", error);
    func();
    eventSource.close();
  };
};

export const getOutletMenu = async (
  foodChainId: string,
  outletId?: string,
  includeUnavailable: boolean = false
) => {
  try {
    const response = await server.get(
      `/v1/user/dishes?foodChainId=${foodChainId}${
        outletId ? `&outletId=${outletId}` : ""
      }${includeUnavailable ? "&includeUnavailable=true" : ""}`
    );
    return response.data;
  } catch (error) {
    console.error("Error fetching menu:", error);
    return error;
  }
};

export const getConversation = async (outletId: string) => {
  const userId = localStorage.getItem("userId");
  if (!userId) return;
  try {
    const response = await server.get(
      `/v1/user/get_conversation?userId=${userId}&outletId=${outletId}`
    );
    return response.data;
  } catch (error) {
    console.error("Error fetching layouts:", error);
    return error;
  }
};

export const getAllConversation = async () => {
  const userId = localStorage.getItem("userId");
  try {
    const response = await server.get(
      `/v1/user/get_all_conversations?userId=${userId}`
    );
    return response.data;
  } catch (error) {
    console.error("Error fetching layouts:", error);
    return error;
  }
};

export const clearConversation = async (outletId: string) => {
  const userId = localStorage.getItem("userId");
  try {
    const response = await server.delete(
      `/v1/user/delete_conversation?userId=${userId}&outletId=${outletId}`
    );
    return response.data;
  } catch (error) {
    console.error("Error fetching layouts:", error);
    return error;
  }
};

export const userLogin = async (email: string, password: string) => {
  try {
    const response = await server.post(`/v1/user/login`, {
      email,
      password,
    });
    return response.data;
  } catch (error) {
    console.error("Error fetching layouts:", error);
    return error;
  }
};

export const userRegister = async (
  email: string,
  password: string,
  name: string,
  phone: string
) => {
  try {
    const response = await server.post(`/v1/user/register`, {
      email,
      password,
      name,
      phone,
    });
    return response.data;
  } catch (error) {
    console.error("Error fetching layouts:", error);
    return error;
  }
};

export const userRegisterWithPhone = async (
  phone: string,
  name: string,
  address?: string
) => {
  try {
    const response = await server.post(`/v1/user/register-phone`, {
      phone,
      name,
      address,
    });
    return response.data;
  } catch (error) {
    console.error("Error registering with phone:", error);
    return error;
  }
};

export const userLoginWithPhone = async (phone: string) => {
  try {
    const response = await server.post(`/v1/user/login-phone`, {
      phone,
    });
    return response.data;
  } catch (error) {
    console.error("Error logging in with phone:", error);
    return error;
  }
};

export const getProfile = async () => {
  const userId = localStorage.getItem("userId");
  try {
    const response = await server.get(`/v1/user/user_profile/${userId}`);
    return response.data;
  } catch (error) {
    console.error("Error fetching layouts:", error);
    return error;
  }
};

export const updateUserProfile = async (profile: any) => {
  const userId = localStorage.getItem("userId");
  try {
    const response = await server.put(
      `/v1/user/update_profile/${userId}`,
      {
        ...profile,
      },
      {
        headers: {
          Authorization: `Bearer ${localStorage.getItem("user-token")}`,
        },
      }
    );
    return response.data;
  } catch (error) {
    console.error("Error fetching layouts:", error);
    return error;
  }
};

export const updateUserPassword = async (data: {
  oldPassword: string;
  newPassword: string;
}) => {
  const userId = localStorage.getItem("userId");
  try {
    const response = await server.put(
      `/v1/user/update_password/${userId}`,
      {
        ...data,
      },
      {
        headers: {
          Authorization: `Bearer ${localStorage.getItem("user-token")}`,
        },
      }
    );
    return response.data;
  } catch (error) {
    return error;
  }
};

// Order-related functions
export const createOrder = async (orderData: any) => {
  try {
    const response = await server.post("/v1/user/create-order", orderData, {
      headers: {
        Authorization: `Bearer ${localStorage.getItem("user-token")}`,
      },
    });
    return response.data;
  } catch (error) {
    console.error("Error creating order:", error);
    return error;
  }
};

export const getOrderHistory = async () => {
  try {
    const response = await server.get("/v1/user/orders", {
      headers: {
        Authorization: `Bearer ${localStorage.getItem("user-token")}`,
      },
    });
    return response.data;
  } catch (error) {
    console.error("Error fetching order history:", error);
    return error;
  }
};

export const getOrderStatus = async (orderId: string) => {
  try {
    const response = await server.get(`/v1/user/orders/${orderId}`, {
      headers: {
        Authorization: `Bearer ${localStorage.getItem("user-token")}`,
      },
    });
    return response.data;
  } catch (error) {
    console.error("Error fetching order status:", error);
    return error;
  }
};

export const cancelOrder = async (
  orderId: string,
  cancellationReason?: string
) => {
  try {
    const response = await server.put(
      `/v1/user/orders/${orderId}/cancel`,
      {
        cancellationReason: cancellationReason || "Cancelled by customer",
      },
      {
        headers: {
          Authorization: `Bearer ${localStorage.getItem("user-token")}`,
        },
      }
    );
    return response.data;
  } catch (error) {
    console.error("Error cancelling order:", error);
    return error;
  }
};

export const updateOrderItems = async (
  orderId: string,
  items: any[],
  couponCode?: string,
  couponDiscount?: number,
  appliedOffers?: any[]
) => {
  try {
    const response = await server.put(
      `/v1/user/orders/${orderId}/update-items`,
      {
        items,
        couponCode,
        couponDiscount,
        appliedOffers,
      },
      {
        headers: {
          Authorization: `Bearer ${localStorage.getItem("user-token")}`,
        },
      }
    );
    return response.data;
  } catch (error) {
    console.error("Error updating order items:", error);
    return error;
  }
};

export const submitFoodChainRegistrationRequest = async (data: {
  contactPersonName: string;
  email: string;
  phone: string;
  businessName: string;
  businessType: string;
  subcategory?: string;
  city: string;
  state: string;
  address?: string;
  message?: string;
  estimatedOutlets?: number;
  website?: string;
}) => {
  try {
    const response = await server.post(
      "/v1/food-chain-registration-request",
      data
    );
    return response.data;
  } catch (error) {
    console.error("Registration request failed:", error);
    return error;
  }
};

export const verifyGoogleToken = async (token: string, userData: any) => {
  try {
    const response = await server.post("/v1/user/auth/google/verify", {
      token,
      userData,
    });
    return response.data;
  } catch (error) {
    console.error("Google token verification failed:", error);
    return error;
  }
};

export const completeUserProfile = async (token: string, userData: any) => {
  try {
    const response = await server.post("/v1/user/auth/google/verify", {
      token,
      userData,
    });
    return response.data;
  } catch (error) {
    console.error("Google token verification failed:", error);
    return error;
  }
};
