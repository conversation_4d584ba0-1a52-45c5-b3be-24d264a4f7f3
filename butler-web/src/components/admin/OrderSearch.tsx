import { Ch<PERSON>ronDown, Ch<PERSON>ronUp, X } from "lucide-react";
import { Search } from "lucide-react";
import React, { useState, useEffect } from "react";
import { Input } from "../ui/input";
import { Order } from "@/types/order";

const OrderSearch = ({ orders }: { orders: Order[] }) => {
  const [searchOn, setSearchOn] = useState<boolean>(false);
  const [searchQuery, setSearchQuery] = useState<string>("");
  const [filteredOrders, setFilteredOrders] = useState<Order[]>([]);
  const [currentIndex, setCurrentIndex] = useState<number>(-1);

  useEffect(() => {
    if (searchQuery) {
      const filteredOrders = orders.filter(
        (order) =>
          order?.orderNumber?.toString()?.includes(searchQuery) ||
          order?.userId?.name
            ?.toLowerCase()
            ?.includes(searchQuery.toLowerCase()) ||
          order?.outletId?.name
            ?.toLowerCase()
            .includes(searchQuery.toLowerCase()) ||
          order?.userId?.phone?.toString().includes(searchQuery) ||
          order?.items?.some((item) =>
            item?.dishId?.name
              ?.toLowerCase()
              .includes(searchQuery.toLowerCase())
          )
      );
      if (filteredOrders.length > 0) {
        setCurrentIndex(0);
      }
      setFilteredOrders(filteredOrders);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [searchQuery]);

  useEffect(() => {
    if (currentIndex !== null) {
      console.log("scrolling to", filteredOrders[currentIndex]?._id);
      document
        .getElementById(`order-${filteredOrders[currentIndex]?._id}`)
        ?.scrollIntoView({
          behavior: "smooth",
        });
    }
  }, [currentIndex, filteredOrders]);

  return (
    <div className="flex items-center gap-2 absolute right-0 top-20 shadow-lg p-2 rounded-l-md bg-white border-2 border-gray-800 border-r-0">
      {searchOn ? (
        <>
          <Input
            placeholder="Search"
            value={searchQuery}
            className="w-64"
            onChange={(e) => setSearchQuery(e.target.value)}
            onKeyDown={(e) => {
              if (e.key === "Enter") {
                setCurrentIndex(
                  currentIndex === filteredOrders.length - 1
                    ? 0
                    : currentIndex + 1
                );
              } else if (e.key === "ArrowUp") {
                setCurrentIndex(
                  currentIndex === 0
                    ? filteredOrders.length - 1
                    : currentIndex - 1
                );
              } else if (e.key === "ArrowDown") {
                setCurrentIndex(
                  currentIndex === filteredOrders.length - 1
                    ? 0
                    : currentIndex + 1
                );
              } else if (e.key === "Escape") {
                setSearchOn(false);
                setSearchQuery("");
                setCurrentIndex(-1);
                setFilteredOrders([]);
              }
            }}
          />
          {filteredOrders.length > 0 && (
            <>
              {currentIndex + 1} / {filteredOrders.length}
              <ChevronUp
                onClick={() =>
                  setCurrentIndex(
                    currentIndex === 0
                      ? filteredOrders.length - 1
                      : currentIndex - 1
                  )
                }
                className="cursor-pointer"
              />
              <ChevronDown
                onClick={() =>
                  setCurrentIndex(
                    currentIndex === filteredOrders.length - 1
                      ? 0
                      : currentIndex + 1
                  )
                }
                className="cursor-pointer"
              />
            </>
          )}
          <X
            onClick={() => {
              setSearchOn(false);
              setSearchQuery("");
              setCurrentIndex(-1);
              setFilteredOrders([]);
            }}
            className="cursor-pointer"
          />
        </>
      ) : (
        <Search onClick={() => setSearchOn(true)} className="cursor-pointer" />
      )}
    </div>
  );
};

export default OrderSearch;
