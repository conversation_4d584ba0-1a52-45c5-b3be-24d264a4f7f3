"use client";

import { useState } from "react";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Checkbox } from "@/components/ui/checkbox";
import { toast } from "sonner";
import { CheckCircle, Clock, Calendar, Loader2 } from "lucide-react";
import { format } from "date-fns";

interface OrderItem {
  dishId?: {
    _id: string;
    name: string;
    price: number;
  } | null;
  dishName?: string; // Stored dish name for deleted dishes
  quantity: number;
  price: number;
  addOns?: Array<{
    addOnId?: string;
    _id?: string;
    name: string;
    price: number;
    quantity?: number;
  }>;
  isServed?: boolean;
  servedAt?: string;
  servedBy?: {
    _id: string;
    name: string;
  };
  servedQuantity?: number;
}

interface DishServingStatusProps {
  items: OrderItem[];
  isAdmin?: boolean;
  onServingStatusChange?: (itemIndex: number, isServed: boolean) => void;
}

export default function DishServingStatus({
  items,
  isAdmin = false,
  onServingStatusChange,
}: DishServingStatusProps) {
  const [updatingItems, setUpdatingItems] = useState<Set<number>>(new Set());

  const handleServingToggle = async (
    itemIndex: number,
    currentStatus: boolean
  ) => {
    if (!isAdmin || !onServingStatusChange) return;

    const newStatus = !currentStatus;
    setUpdatingItems((prev) => new Set(prev).add(itemIndex));

    try {
      await onServingStatusChange(itemIndex, newStatus);
      toast.success(
        newStatus ? "Dish marked as served" : "Dish marked as not served"
      );
    } catch (error) {
      console.error("Error updating serving status:", error);
      toast.error("Failed to update serving status");
    } finally {
      setUpdatingItems((prev) => {
        const newSet = new Set(prev);
        newSet.delete(itemIndex);
        return newSet;
      });
    }
  };

  const getServingStatusBadge = (item: OrderItem) => {
    if (item.isServed) {
      return (
        <Badge
          variant="default"
          className="bg-green-100 text-green-800 border-green-200"
        >
          <CheckCircle className="w-3 h-3 mr-1" />
          <div className="hidden md:block">Served</div>
        </Badge>
      );
    } else {
      return (
        <Badge
          variant="secondary"
          className="bg-yellow-100 text-yellow-800 border-yellow-200"
        >
          <Clock className="w-3 h-3 mr-1" />
          <div className="hidden md:block">Pending</div>
        </Badge>
      );
    }
  };

  const allItemsServed = items.every((item) => item.isServed);
  const servedCount = items.filter((item) => item.isServed).length;

  return (
    <div className="space-y-6">
      {/* Enhanced Overall Status */}
      <Card className="shadow-lg border-0 rounded-3xl overflow-hidden bg-gradient-to-br from-white to-gray-50">
        <CardContent className="p-6">
          <div className="flex items-center justify-between ">
            <div className="flex items-center justify-between gap-4">
              <div
                className={`p-3 rounded-2xl shadow-lg transition-all duration-300 ${
                  allItemsServed ? "bg-green-500" : "bg-blue-500"
                }`}
              >
                {allItemsServed ? (
                  <CheckCircle className="w-7 h-7 text-white" />
                ) : (
                  <Clock className="w-7 h-7 text-white" />
                )}
              </div>
              <div>
                <p className="text-sm text-gray-600 flex items-center gap-2">
                  <span className="bg-gray-100 rounded-full px-3 py-1 font-semibold">
                    {servedCount} / {items.length}
                  </span>
                  served
                </p>
              </div>
            </div>
          </div>

          {/* Enhanced Progress Bar */}
          <div className="space-y-2">
            <div className="flex justify-between text-xs font-medium text-gray-600">
              <span>Progress</span>
              <span>{Math.round((servedCount / items.length) * 100)}%</span>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-3 overflow-hidden">
              <div
                className={`h-3 rounded-full transition-all duration-700 ease-out relative overflow-hidden ${
                  allItemsServed
                    ? "bg-gradient-to-r from-green-500 to-green-600"
                    : "bg-gradient-to-r from-blue-500 to-blue-600"
                }`}
                style={{ width: `${(servedCount / items.length) * 100}%` }}
              >
                {/* Shimmer effect */}
                <div className="absolute inset-0 bg-white/30 translate-x-[-100%] animate-shimmer" />
              </div>
            </div>
          </div>

          {/* Progress Stats */}
          <div className="grid grid-cols-3 gap-4 mt-4">
            <div className="text-center p-3 bg-gray-50 rounded-xl">
              <div className="text-lg font-bold text-gray-900">
                {items.length}
              </div>
              <div className="text-xs text-gray-600">Total Items</div>
            </div>
            <div className="text-center p-3 bg-green-50 rounded-xl">
              <div className="text-lg font-bold text-green-700">
                {servedCount}
              </div>
              <div className="text-xs text-gray-600">Served</div>
            </div>
            <div className="text-center p-3 bg-orange-50 rounded-xl">
              <div className="text-lg font-bold text-orange-700">
                {items.length - servedCount}
              </div>
              <div className="text-xs text-gray-600">Pending</div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Enhanced Individual Dishes */}
      <div className="space-y-4">
        {items.map((item, index) => (
          <Card
            key={index}
            className={`transition-all duration-300 border-0 rounded-2xl shadow-md hover:shadow-lg transform hover:scale-[1.01] ${
              item.isServed
                ? "bg-gradient-to-r from-green-50 to-emerald-50 border-green-200 shadow-green-100"
                : "bg-white hover:shadow-xl"
            }`}
          >
            <CardContent className="p-5">
              <div className="flex items-start justify-between gap-4">
                <div className="flex-1">
                  <div className="flex items-center gap-4 mb-3">
                    {isAdmin && (
                      <div className="flex-shrink-0">
                        <Checkbox
                          checked={item.isServed || false}
                          onCheckedChange={() =>
                            handleServingToggle(index, item.isServed || false)
                          }
                          disabled={updatingItems.has(index)}
                          className="h-5 w-5 rounded-lg border-2 data-[state=checked]:bg-green-500 data-[state=checked]:border-green-500"
                        />
                      </div>
                    )}

                    <div className="flex-1">
                      <h4 className="font-bold text-lg text-gray-900 mb-2">
                        {item.dishId?.name || item.dishName || "Deleted Dish"}
                      </h4>

                      {/* Enhanced Item Details */}
                      <div className="grid grid-cols-2 md:grid-cols-2 lg:grid-cols-4 gap-3 mb-3">
                        <div className="bg-gray-50 rounded-xl p-3">
                          <div className="text-xs text-gray-600 mb-1">
                            Quantity
                          </div>
                          <div className="font-semibold text-gray-900">
                            {item.quantity}
                          </div>
                        </div>

                        <div className="bg-green-50 rounded-xl p-3">
                          <div className="text-xs text-green-600 mb-1">
                            Served
                          </div>
                          <div className="font-semibold text-green-800">
                            {item.servedQuantity}
                          </div>
                        </div>
                      </div>
                      <div className="bg-purple-50 rounded-xl p-3">
                        <div className="text-xs text-purple-600 mb-1">
                          Total
                        </div>
                        <div className="font-bold text-purple-800">
                          ₹
                          {(() => {
                            const addOnsTotal = Array.isArray(item.addOns)
                              ? item.addOns.reduce(
                                  (sum, ao) =>
                                    sum + ao.price * (ao.quantity || 1),
                                  0
                                )
                              : 0;
                            return (
                              (item.price * item.quantity) 
                              + addOnsTotal
                            ).toFixed(2);
                          })()}
                        </div>
                      </div>

                      {/* Enhanced Add-ons Display */}
                      {Array.isArray(item.addOns) && item.addOns.length > 0 && (
                        <div className="bg-amber-50 rounded-xl p-4 border border-amber-200">
                          <div className="flex items-center gap-2 mb-3">
                            <span className="text-amber-600 text-lg">🍽️</span>
                            <div className="text-sm font-semibold text-amber-800">
                              Add-ons
                            </div>
                          </div>
                          <div className="grid gap-2">
                            {item.addOns.map((addOn, addOnIndex) => (
                              <div
                                key={addOnIndex}
                                className="flex justify-between items-center bg-white rounded-lg p-2 border border-amber-100"
                              >
                                <span className="text-sm text-gray-700">
                                  {addOn.name} × {addOn.quantity || 1}
                                </span>
                                <span className="text-sm font-semibold text-gray-900">
                                  +₹
                                  {(
                                    (addOn.price || 0) * (addOn.quantity || 1)
                                  ).toFixed(2)}
                                </span>
                              </div>
                            ))}
                          </div>
                        </div>
                      )}
                    </div>
                  </div>

                  {/* Enhanced Serving Details */}
                  {item.isServed && item.servedAt && (
                    <div className="mt-4 p-4 bg-green-100 rounded-2xl border-2 border-green-200">
                      <div className="flex items-center gap-3">
                        <div className="p-2 bg-green-500 rounded-xl">
                          <Calendar className="w-4 h-4 text-white" />
                        </div>
                        <div>
                          <div className="text-sm font-semibold text-green-800">
                            Served Successfully
                          </div>
                          <div className="text-xs text-green-600">
                            {format(
                              new Date(item.servedAt),
                              "MMM dd, yyyy 'at' hh:mm a"
                            )}
                          </div>
                        </div>
                        <div className="ml-auto">
                          <span className="text-green-600 text-xl">✅</span>
                        </div>
                      </div>
                    </div>
                  )}
                </div>

                {/* Enhanced Status and Actions */}
                <div className="flex flex-col items-end gap-3">
                  <div className="flex-shrink-0">
                    {getServingStatusBadge(item)}
                  </div>

                  {isAdmin && !item.isServed && (
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() =>
                        handleServingToggle(index, item.isServed || false)
                      }
                      disabled={updatingItems.has(index)}
                      className="rounded-xl border-2 border-green-300 text-green-700 hover:bg-green-100 font-semibold px-4 py-2 transition-all duration-200 hover:scale-105 active:scale-95"
                    >
                      {updatingItems.has(index) ? (
                        <>
                          <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                          Updating...
                        </>
                      ) : (
                        <>
                          <CheckCircle className="w-4 h-4 mr-2" />
                          Mark as Served
                        </>
                      )}
                    </Button>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Enhanced Admin Summary */}
      {isAdmin && (
        <Card className="shadow-xl border-0 rounded-3xl overflow-hidden">
          <div className="bg-gradient-to-r from-blue-500 to-indigo-600 p-6">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-4">
                <div className="p-3 bg-white/20 rounded-2xl backdrop-blur-sm">
                  <span className="text-2xl">👨‍🍳</span>
                </div>
                <div>
                  <h4 className="font-bold text-xl text-white">
                    Kitchen Status
                  </h4>
                  <p className="text-blue-100 text-sm">
                    {allItemsServed
                      ? "🎉 All dishes have been served. Order is complete!"
                      : `⏳ ${items.length - servedCount} dish${
                          items.length - servedCount !== 1 ? "es" : ""
                        } still need to be served.`}
                  </p>
                </div>
              </div>

              {!allItemsServed && (
                <Button
                  variant="secondary"
                  size="sm"
                  onClick={() => {
                    items.forEach((item, index) => {
                      if (!item.isServed) {
                        handleServingToggle(index, false);
                      }
                    });
                  }}
                  className="bg-white text-blue-700 hover:bg-blue-50 rounded-2xl px-6 py-3 font-bold shadow-lg hover:shadow-xl transition-all duration-200 hover:scale-105"
                >
                  <CheckCircle className="w-4 h-4 mr-2" />
                  Mark All as Served
                </Button>
              )}
            </div>

            {allItemsServed && (
              <div className="mt-4 p-4 bg-white/10 rounded-2xl backdrop-blur-sm">
                <div className="flex items-center justify-center gap-2 text-white">
                  <span className="text-2xl">🎊</span>
                  <span className="font-semibold">Order Complete!</span>
                  <span className="text-2xl">🎊</span>
                </div>
              </div>
            )}
          </div>
        </Card>
      )}

      <style jsx>{`
        @keyframes shimmer {
          0% {
            transform: translateX(-100%);
          }
          100% {
            transform: translateX(100%);
          }
        }
        .animate-shimmer {
          animation: shimmer 2s infinite;
        }
      `}</style>
    </div>
  );
}
