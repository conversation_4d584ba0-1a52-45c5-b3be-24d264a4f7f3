/* eslint-disable react-hooks/exhaustive-deps */
/* eslint-disable @typescript-eslint/no-explicit-any */
"use client";
import React, { useRef, useState, useEffect, Suspense } from "react";
import { useSearchParams, useRouter } from "next/navigation";
import Image from "next/image";
import "./chat.css";
import { useTheme } from "@/contexts/ThemeContext";
import { useAuth } from "@/contexts/AuthContext";
import { Input } from "@/components/ui/input";
import { Conversation, Dish } from "@/app/type";
import { formatTime } from "@/app/helper/time";
import { Button } from "@/components/ui/button";
import { Icon } from "@iconify/react/dist/iconify.js";
import {
  ChevronLeft,
  Minus,
  Plus,
  ShoppingBag,
  ShoppingCart,
  MapPin,
  Trash2,
  LogOut,
  Clock,
  X,
} from "lucide-react";

import {
  botName,
  firstLetterExtractor,
  formatStringToHtml,
  generateChatPlaceHolder,
  generateInitialMessages,
} from "@/app/helper/chat";
import {
  clearConversation,
  agenticConversation,
  getConversation,
  getOutletMenu,
} from "@/server/user";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuLabel,
  DropdownMenuTrigger,
  DropdownMenuItem,
} from "@/components/ui/dropdown-menu";
import { Separator } from "@/components/ui/separator";
import { Badge } from "@/components/ui/badge";
import { ScrollArea } from "@/components/ui/scroll-area";
import useBackendCart from "@/hooks/useBackendCart";
import { clearCart as clearCartAPI } from "@/server/cart";
import { stringReducer } from "@/app/helper/general";
import StreamingText from "@/components/ui/TypewriterText";
import OffersCarousel from "@/components/custom/offers/OffersCarousel";
import { toast } from "sonner";

// Type for applied offers from backend cart (currently unused but kept for future use)
// interface AppliedOffer {
//   offerId: string;
//   offerName: string;
//   offerType: string;
//   discountAmount: number;
//   discountType: string;
//   applicableItems?: any[];
//   appliedAt: string;
// }
import MobileVoiceInput from "@/components/custom/users/MobileVoiceInput";
import TextToSpeech from "@/components/custom/users/TextToSpeech";
import { useLanguagePreference } from "../../../../hooks/useLanguagePreference";
import LanguageSelector from "../../../../components/LanguageSelector";

const Page = () => {
  const {
    cart,
    appliedOffers,
    cartTotals,
    // loading: cartLoading, // Unused for now
    addToCart: addToCartBackend,
    removeFromCart: removeFromCartBackend,
    updateItemQuantity,
    refreshCart,
    // getCartItemCount, // Unused for now
    isItemInCart,
    getItemQuantity,
  } = useBackendCart();
  const router = useRouter();
  const {
    language,
    setLanguage,
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    isLoading: languageLoading,
  } = useLanguagePreference();
  const params = useSearchParams();
  const outletId = params?.get("outletId") || "";
  const [isCartMenuOpen, setIsCartMenuOpen] = useState(false);
  const foodChainId = params?.get("chainId") || "";

  // Helper functions to get foodChainId and outletId
  const getFoodChainId = () => {
    return params?.get("chainId") || localStorage.getItem("chainId") || "";
  };

  const getOutletId = () => {
    return params?.get("outletId") || localStorage.getItem("outletId") || "";
  };
  const [isOpen, setIsOpen] = useState(false);
  const [userMessage, setUserMessage] = useState("");
  const chatContainerRef = useRef<HTMLDivElement>(null);
  // Track the last message element to scroll to its head instead of bottom
  const lastMessageRef = useRef<HTMLDivElement | null>(null);
  const [isMenuClicked, setIsMenuClicked] = useState(false);
  const { theme, isLoading, setChainAndOutlet } = useTheme();
  const { logout } = useAuth();
  const [responseLoading, setResponseLoading] = useState(false);
  const [placeHolder, setPlaceholder] = useState(generateChatPlaceHolder());
  const [conversationHistory, setConversationHistory] = useState<
    Conversation[]
  >([]);
  const [selectedCategory, setSelectedCategory] = useState(null);
  const [menu, setMenu] = useState<{ category: string; dishes: Dish[] }[]>([]);
  // Always use agentic mode - no toggle needed

  // Reset animation after some time of inactivity
  useEffect(() => {
    let timer: any;
    if (isMenuClicked) {
      timer = setTimeout(() => {
        setIsMenuClicked(false);
      }, 60000); // Reset after 1 minute of inactivity
    }
    return () => clearTimeout(timer);
  }, [isMenuClicked]);

  // Handle category selection
  const handleCategorySelect = (category: any) => {
    setSelectedCategory(category);
    if (category === "") return;
    document.getElementById(category)?.scrollIntoView({ behavior: "smooth" });
  };

  const handleAddToCart = async (item: Dish) => {
    if (isItemInCart(item._id!)) {
      // If item is already in cart, remove it
      await removeFromCartBackend(item._id!);
    } else {
      // Add item to cart directly - addons will be selected on checkout page
      await addToCartBackend(item, 1);
    }
  };

  const handleUpdateQuantity = async (
    dish: Dish,
    action: "increase" | "decrease"
  ) => {
    const currentQuantity = getItemQuantity(dish._id!);

    if (action === "increase") {
      await updateItemQuantity(dish._id!, currentQuantity + 1);
    } else if (action === "decrease") {
      if (currentQuantity === 1) {
        await removeFromCartBackend(dish._id!);
      } else {
        await updateItemQuantity(dish._id!, currentQuantity - 1);
      }
    }
  };

  useEffect(() => {
    if (foodChainId) {
      setChainAndOutlet(foodChainId);
    }
  }, [foodChainId]);

  // Note: Auto-apply offers is now handled by the backend cart system

  const getOutletMenuFunc = async () => {
    if (!foodChainId || !outletId) return;
    const response = await getOutletMenu(String(foodChainId), String(outletId));
    const set = new Set(response.data.map((item: any) => item.category.name));

    // Add categories message
    setConversationHistory([
      {
        sender: botName.lowerCase,
        message: generateInitialMessages(),
        time: new Date().getTime(),
      },
      {
        sender: "user",
        message: "Can you please show me the menu?",
        time: new Date().getTime(),
      },
      {
        sender: botName.lowerCase,
        message: `We are serving the following categories: ${[...set].join(
          ", "
        )}`,
        time: new Date().getTime(),
        dishes: response.data,
        suggestedQuestions: [
          response.data[0]?.name ? `What should I try in ${[...set][0]}?` : "",
          response.data[0]?.name
            ? `Add ${response.data[0]?.name} to my order`
            : "",
        ],
      },
    ]);

    // Use saved recommended dishes if available, otherwise use all dishes
    const dishesToShow = response.data;
    updateConversationWithGroupedDishes(dishesToShow, [
      response.data[0]?.name ? `What should I try in ${[...set][0]}?` : "",
      response.data[0]?.name ? `Add ${response.data[0]?.name} to my order` : "",
    ]);

    setMenu(groupDishesByCategory(response.data) as any);

    // Get previous conversations
    const conversationData = await getConversation(String(outletId));
    const hasExistingMessages =
      conversationData?.data?.messages &&
      conversationData.data.messages.length > 0;

    if (hasExistingMessages) {
      setConversationHistory((prev) => [
        ...prev,
        ...conversationData.data.messages,
      ]);

      // For returning users (with existing messages), scroll to bottom
      setTimeout(() => scrollToBottom(), 300);
    } else {
      // For new users (no previous messages), scroll to top
      setTimeout(() => scrollToTop(), 300);
    }
  };

  useEffect(() => {
    // Then fetch menu and previous conversations
    setTimeout(() => {
      getOutletMenuFunc();
    }, 100);
  }, []);

  // Note: Scrolling is now handled in getOutletMenuFunc based on whether user is new or returning

  // Scroll to the very bottom of the chat (for returning users)
  const scrollToBottom = () => {
    if (!chatContainerRef.current) return;

    const scrollContainer = chatContainerRef.current.querySelector(
      ".overflow-y-auto"
    ) as HTMLElement | null;

    if (scrollContainer) {
      scrollContainer.scrollTo({
        top: scrollContainer.scrollHeight,
        behavior: "smooth",
      });
    } else {
      chatContainerRef.current.scrollTo({
        top: chatContainerRef.current.scrollHeight,
        behavior: "smooth",
      });
    }
  };

  // Scroll to the top of the chat (for new users)
  const scrollToTop = () => {
    if (!chatContainerRef.current) return;

    const scrollContainer = chatContainerRef.current.querySelector(
      ".overflow-y-auto"
    ) as HTMLElement | null;

    if (scrollContainer) {
      scrollContainer.scrollTo({
        top: 0,
        behavior: "smooth",
      });
    } else {
      chatContainerRef.current.scrollTo({
        top: 0,
        behavior: "smooth",
      });
    }
  };

  // Scroll to the head (top) of the last message (for new messages)
  const scrollToLastMessageHead = () => {
    if (!chatContainerRef.current) return;

    // Try to find the last message element marked with data-last-message
    const lastMsgEl = chatContainerRef.current.querySelector(
      '[data-last-message="true"]'
    ) as HTMLElement | null;

    const target = lastMsgEl || lastMessageRef.current;
    if (target) {
      // Scroll the container so that the last message is at the top of the viewport
      target.scrollIntoView({ behavior: "smooth", block: "start" });
    } else {
      // Fallback to scrolling to bottom
      scrollToBottom();
    }
  };

  const handleClearConversation = () => {
    clearConversation(String(outletId)).then(() => {
      router.push(`/conversations`);
    });
    setConversationHistory([]);
  };

  useEffect(() => {
    // Scroll to the head of the last message whenever history or loading changes
    const timer = setTimeout(() => {
      scrollToLastMessageHead();
    }, 150);
    return () => clearTimeout(timer);
  }, [conversationHistory, responseLoading]);

  // Additional scroll effect for when new messages are added
  useEffect(() => {
    if (conversationHistory.length > 0) {
      const timer = setTimeout(() => {
        scrollToLastMessageHead();
      }, 200); // Ensure content rendered
      return () => clearTimeout(timer);
    }
  }, [conversationHistory.length]);

  // On component mount, align to the head of current last message after initial render
  useEffect(() => {
    const timer = setTimeout(() => {
      scrollToLastMessageHead();
    }, 500);
    return () => clearTimeout(timer);
  }, []);

  const handleNewMessage = (message: string) => {
    if (responseLoading) return;

    setResponseLoading(true);

    // Add user's message to conversation history
    setConversationHistory((prev) => [
      ...prev,
      { sender: "user", message, time: new Date().getTime() },
    ]);

    // Align to head of last message after adding user message
    setTimeout(() => scrollToLastMessageHead(), 100);

    let fullText = "";

    // Stream data from the backend
    const days = 1;
    const dayAgo = new Date(Date.now() - days * 24 * 60 * 60 * 1000);

    const recentConversation = conversationHistory
      .slice(3)
      .slice(-6) // Get the last 6 entries
      .filter((item) => item?.time && new Date(item?.time) >= dayAgo);

    // Always use agentic conversation
    const conversationMethod = agenticConversation;

    conversationMethod(
      String(foodChainId),
      userMessage || message,
      (text: string) => {
        fullText += text;
        setConversationHistory((prev) => {
          // Update the butler's latest message or create a new one
          const lastMessage = prev[prev.length - 1];
          if (lastMessage?.sender === botName.lowerCase) {
            return [
              ...prev.slice(0, -1),
              { ...lastMessage, message: fullText },
            ];
          }
          return [
            ...prev,
            {
              sender: botName.lowerCase,
              message: fullText,
              time: new Date().getTime(),
            },
          ];
        });

        // Align view to the head of the last message during streaming
        setTimeout(() => scrollToLastMessageHead(), 50);
      },
      (
        dishes: Dish[],
        faqSuggestions: string[],
        cartOperation?: any,
        fallbackActions?: any,
        metadata?: any
      ) => {
        // Always update conversation, even with empty dishes
        if (faqSuggestions?.length > 0) {
          setPlaceholder(faqSuggestions[0]);
        }

        // Handle cart operations
        if (cartOperation) {
          // Handle order placement success
          if (cartOperation.operation === "order" && cartOperation.success) {
            // Show success toast
            toast.success(
              `Order #${cartOperation.orderNumber} placed successfully! Redirecting to tracking...`
            );

            // Redirect to order tracking page
            if (cartOperation.redirectTo) {
              setTimeout(() => {
                router.push(cartOperation.redirectTo);
              }, 2000); // Give user time to read the success message
            }
          }

          // Handle other successful cart operations
          else if (
            cartOperation.success &&
            cartOperation.operation !== "order"
          ) {
            // Show success toast for add/remove operations
            if (cartOperation.operation === "add") {
              toast.success(
                `Added ${cartOperation.dish?.name || "item"} to cart`
              );
            } else if (cartOperation.operation === "remove") {
              toast.success(
                `Removed ${cartOperation.dish?.name || "item"} from cart`
              );
            } else if (cartOperation.operation === "clear") {
              toast.success("Cart cleared successfully");
            }
          }

          // Handle failed cart operations
          else if (!cartOperation.success) {
            // Show error toast for failed operations
            if (cartOperation.operation === "order") {
              toast.error(cartOperation.message || "Failed to place order");
            } else {
              toast.error(cartOperation.message || "Cart operation failed");
            }
          }
        }

        // Handle AI response with additional data
        if (metadata) {
          // Show fallback actions if cart operation failed
          if (fallbackActions && fallbackActions.length > 0) {
            console.log("🔄 Fallback actions available:", fallbackActions);
            // You can add UI to show fallback actions here
          }
        }

        // Pass dishes (even if empty) to the update function
        updateConversationWithGroupedDishes(
          dishes || [],
          faqSuggestions || [],
          cartOperation
        );
      },
      () => {
        setResponseLoading(false);
        setUserMessage(""); // Clear the user message input

        // Check if message might involve cart operations and refresh cart
        const cartKeywords = [
          "add",
          "remove",
          "delete",
          "cart",
          "order",
          "buy",
          "purchase",
        ];
        const mightInvolveCart = cartKeywords.some((keyword) =>
          message.toLowerCase().includes(keyword)
        );

        if (mightInvolveCart) {
          console.log(
            "🛒 Message might involve cart operations, refreshing cart..."
          );
          setTimeout(() => {
            refreshCart(true); // Silent refresh after potential cart operations
          }, 1500); // Delay to ensure backend operations are complete
        }

        // Final align to head of last message when response is complete
        setTimeout(() => scrollToLastMessageHead(), 200);
      },
      String(outletId),
      recentConversation || [],
      language // Pass the selected language
    );
  };

  // Function to group dishes by category
  const groupDishesByCategory = (dishes: Dish[]) => {
    // Create an object to hold grouped dishes
    let isFeatured = false;
    const groupedDishes: any = {
      Featured: [],
    };

    // Group dishes by their category
    dishes.forEach((dish: any) => {
      const category = dish?.category?.name || "-";
      if (!groupedDishes[category]) {
        groupedDishes[category] = [];
      }
      groupedDishes[category].push(dish);
      if (dish?.isFeatured) {
        isFeatured = true;
        groupedDishes.Featured.push(dish);
      }
    });
    if (!isFeatured) {
      delete groupedDishes.Featured;
    }

    // Convert to array format if needed
    return Object.entries(groupedDishes).map(([category, dishes]) => ({
      category,
      dishes,
    }));
  };

  // Update the conversation history with grouped dishes
  const updateConversationWithGroupedDishes = async (
    dishes: Dish[],
    faqSuggestions: string[],
    cartOperationResult?: any
  ) => {
    // Handle empty dishes array gracefully
    const dishesArray = Array.isArray(dishes) ? dishes : [];
    const groupedDishes =
      dishesArray.length > 0 ? groupDishesByCategory(dishesArray) : [];

    // If there was a cart operation, just refresh the cart (backend already processed it)
    if (cartOperationResult?.success && !cartOperationResult?.skipProcessing) {
      console.log("AI cart operation completed:", {
        operation: cartOperationResult.operation,
        dish: cartOperationResult.dish?.name,
        quantity: cartOperationResult.quantity,
        isDuplicate: cartOperationResult.isDuplicate,
      });

      // Refresh cart after AI operation to ensure UI is updated
      setTimeout(() => {
        console.log("🔄 Refreshing cart after AI operation...");
        refreshCart(true); // Silent refresh
      }, 500); // Small delay to ensure backend operation is complete
    }

    // Update conversation history after cart operation is complete
    setConversationHistory((prev: any) => {
      const lastMessage = prev[prev.length - 1];
      if (lastMessage?.sender === botName.lowerCase) {
        return [
          ...prev.slice(0, -1),
          {
            ...lastMessage,
            groupedDishes: groupedDishes,
            dishes: dishesArray,
            suggestedQuestions: faqSuggestions || [],
            hasDishes: dishesArray.length > 0,
            cartOperation: cartOperationResult, // Add cart operation result
          },
        ];
      }
      return prev;
    });

    // Align to head of last message after updating conversation with dishes
    setTimeout(() => scrollToLastMessageHead(), 150);
  };

  const handleKeyPress = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === "Enter") {
      const input = e.currentTarget;
      if (input.value.trim()) {
        handleNewMessage(input.value);
        input.value = "";
      }
    }
  };

  const handleInputFocus = () => {
    // Handle keyboard overlay on mobile
    if (window.innerWidth <= 768) {
      // Small delay to allow keyboard to start opening
      setTimeout(() => {
        // Scroll to ensure input is visible
        const inputContainer = document.querySelector(".input-container");
        if (inputContainer) {
          inputContainer.scrollIntoView({
            behavior: "smooth",
            block: "end",
            inline: "nearest",
          });
        }
      }, 150);
    }
  };

  useEffect(() => {
    // Add chat page body class
    document.body.classList.add("chat-page-body");

    const setAppHeight = () => {
      const doc = document.documentElement;
      const windowHeight = window.innerHeight;
      const visualViewportHeight =
        window.visualViewport?.height || windowHeight;

      // Use the smaller of the two heights to handle keyboard overlay
      const effectiveHeight = Math.min(windowHeight, visualViewportHeight);

      const vh = effectiveHeight * 0.01;
      doc.style.setProperty("--vh", `${vh}px`);
      doc.style.setProperty("--app-height", `${effectiveHeight}px`);
    };

    const handleVisualViewportChange = () => {
      if (window.visualViewport) {
        const windowHeight = window.innerHeight;
        const visualViewportHeight = window.visualViewport.height;

        // Use visual viewport height when keyboard is open
        const effectiveHeight = Math.min(windowHeight, visualViewportHeight);
        const vh = effectiveHeight * 0.01;

        document.documentElement.style.setProperty("--vh", `${vh}px`);
        document.documentElement.style.setProperty(
          "--app-height",
          `${effectiveHeight}px`
        );
      }
    };

    // Set initial height
    setAppHeight();

    // Listen for resize events
    window.addEventListener("resize", setAppHeight);

    // Listen for visual viewport changes (keyboard open/close)
    if (window.visualViewport) {
      window.visualViewport.addEventListener(
        "resize",
        handleVisualViewportChange
      );
    }

    return () => {
      window.removeEventListener("resize", setAppHeight);
      if (window.visualViewport) {
        window.visualViewport.removeEventListener(
          "resize",
          handleVisualViewportChange
        );
      }
      // Remove chat page body class on cleanup
      document.body.classList.remove("chat-page-body");
    };
  }, []);

  // Enhanced keyboard detection and handling
  useEffect(() => {
    let isKeyboardOpen = false;

    const handleKeyboardToggle = () => {
      if (window.innerWidth <= 768) {
        const currentHeight =
          window.visualViewport?.height || window.innerHeight;
        const windowHeight = window.innerHeight;
        const keyboardHeight = windowHeight - currentHeight;
        const wasKeyboardOpen = isKeyboardOpen;

        // More reliable keyboard detection
        isKeyboardOpen = keyboardHeight > 150;

        if (isKeyboardOpen !== wasKeyboardOpen) {
          const mainContainer = document.querySelector(".main-container");

          if (isKeyboardOpen) {
            document.body.classList.add("keyboard-open");
            if (mainContainer) {
              mainContainer.classList.add("keyboard-open");
            }
            // Prevent background scrolling
            document.body.style.overflow = "hidden";
            document.body.style.position = "fixed";
            document.body.style.width = "100%";
          } else {
            document.body.classList.remove("keyboard-open");
            if (mainContainer) {
              mainContainer.classList.remove("keyboard-open");
            }
            // Restore scrolling
            document.body.style.overflow = "";
            document.body.style.position = "";
            document.body.style.width = "";
          }
        }
      }
    };

    // Listen for both resize and visual viewport changes
    window.addEventListener("resize", handleKeyboardToggle);
    if (window.visualViewport) {
      window.visualViewport.addEventListener("resize", handleKeyboardToggle);
    }

    return () => {
      window.removeEventListener("resize", handleKeyboardToggle);
      if (window.visualViewport) {
        window.visualViewport.removeEventListener(
          "resize",
          handleKeyboardToggle
        );
      }
      // Cleanup
      document.body.classList.remove("keyboard-open");
      document.body.style.overflow = "";
      document.body.style.position = "";
      document.body.style.width = "";
    };
  }, []);

  // Add viewport meta tag for better mobile handling
  useEffect(() => {
    const metaViewport = document.querySelector('meta[name="viewport"]');
    if (metaViewport) {
      metaViewport.setAttribute(
        "content",
        "width=device-width, initial-scale=1, maximum-scale=5, user-scalable=yes, viewport-fit=cover"
      );
    }

    return () => {
      // Restore original viewport on cleanup
      if (metaViewport) {
        metaViewport.setAttribute(
          "content",
          "width=device-width, initial-scale=1, maximum-scale=5, user-scalable=yes, viewport-fit=cover"
        );
      }
    };
  }, []);

  const handleButtonClick = () => {
    const input = document.querySelector("input");
    if (input && input.value.trim()) {
      handleNewMessage(input.value);
      input.value = "";
    }
  };

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div>Loading...</div>
      </div>
    );
  }

  return (
    <div
      className="flex flex-col main-container chat-page" // Added chat-page class
      style={{
        backgroundColor: theme.primaryColor + "20",
        height: "100vh",
        minHeight: "100vh",
        maxHeight: "100vh",
      }}
    >
      <header className="sticky top-0 z-50 bg-white border-b shadow-sm flex-shrink-0 chat-header">
        <div className="max-w-screen-xl mx-auto px-2 py-2 md:px-4 md:py-3 flex items-center justify-between w-full min-h-[60px]">
          <div className="flex items-center gap-1 md:gap-3">
            <Button
              variant="ghost"
              size="icon"
              className="rounded-full h-10 w-10 md:h-8 md:w-8 mobile-touch-target"
              onClick={() => {
                router.push(
                  localStorage.getItem("to-outlets")
                    ? "/outlets"
                    : `/conversations`
                );
                localStorage.removeItem("to-outlets");
              }}
              aria-label="Back to conversations"
            >
              <ChevronLeft className="h-5 w-5" />
            </Button>

            <div id="amoeba"></div>

            <h1 className="font-semibold  text-md md:text-lg">
              {firstLetterExtractor(theme?.name).toUpperCase()}&apos;s{" "}
              {botName?.upperCase}
            </h1>
          </div>

          <div className="flex items-center gap-2">
            {/* Language Selector */}
            <LanguageSelector
              selectedLanguage={language}
              onLanguageChange={setLanguage}
              className="mr-2"
            />

            <DropdownMenu
              open={isCartMenuOpen}
              onOpenChange={(open) => {
                setIsCartMenuOpen(open);
                if (open) setIsMenuClicked(true);
              }}
            >
              <DropdownMenuTrigger asChild>
                <div className="relative">
                  <Button
                    variant={"ghost"}
                    className="rounded-full shadow-md cursor-pointer h-10 w-10 md:h-8 md:w-8 mobile-touch-target"
                    style={{ background: theme.primaryColor, color: "white" }}
                    // onClick={() => router.push("/checkout")}
                  >
                    <ShoppingCart className="h-5 w-5" />
                  </Button>
                  {cart.length != 0 && (
                    <div className="bg-red-500 h-6 w-6 flex justify-center text-white text-xs p-1 rounded-full absolute -top-2 -right-2">
                      {cart.length}
                    </div>
                  )}
                </div>
              </DropdownMenuTrigger>

              <DropdownMenuContent
                className="w-[95vw] max-w-96 p-0 rounded-lg border-0 shadow-lg mr-2 md:mr-6"
                sideOffset={5}
                align="end"
              >
                <div className="flex flex-col h-full">
                  {/* Enhanced Header with Savings Animation */}
                  <div className="relative overflow-hidden">
                    <div
                      className="bg-gradient-to-br p-4 pb-6 relative"
                      style={{
                        backgroundImage: `linear-gradient(135deg, ${theme.primaryColor}ee, ${theme.primaryColor}cc, ${theme.primaryColor}99)`,
                      }}
                    >
                      {/* Decorative background elements */}
                      <div className="absolute top-0 right-0 w-32 h-32 bg-white/10 rounded-full -translate-y-16 translate-x-16" />
                      <div className="absolute bottom-0 left-0 w-24 h-24 bg-white/5 rounded-full translate-y-12 -translate-x-12" />

                      <div className="relative z-10">
                        <DropdownMenuLabel className="text-white text-xl font-bold mb-2 flex items-center gap-3">
                          <div className="p-2 bg-white/20 rounded-xl backdrop-blur-sm">
                            <ShoppingCart size={22} className="text-white" />
                          </div>
                          <div>
                            <span>Your Order</span>
                            <div className="bg-white/20 rounded-full px-3 py-1 ml-3 inline-block">
                              <span className="text-sm font-medium">
                                {cart.length} items
                              </span>
                            </div>
                          </div>
                        </DropdownMenuLabel>

                        <div className="text-white text-sm">
                          {appliedOffers.length > 0 ? (
                            <div className="space-y-2">
                              <div className="flex items-center gap-2">
                                <span className="line-through opacity-70 text-base">
                                  ₹{cartTotals.subtotal.toFixed(2)}
                                </span>
                                <span className="font-black text-xl">
                                  ₹{cartTotals.finalTotal.toFixed(2)}
                                </span>
                              </div>
                              <div className="flex items-center gap-2 bg-green-400/20 rounded-full px-3 py-1.5 backdrop-blur-sm">
                                <span className="text-lg">🎉</span>
                                <span className="text-green-100 font-medium text-sm">
                                  {appliedOffers.length} offer
                                  {appliedOffers.length > 1 ? "s" : ""} applied
                                  {cartTotals.totalOfferDiscount > 0 && (
                                    <span className="ml-2 bg-green-500/30 rounded-full px-2 py-0.5 text-xs">
                                      ₹
                                      {cartTotals.totalOfferDiscount.toFixed(2)}{" "}
                                      saved
                                    </span>
                                  )}
                                </span>
                              </div>
                            </div>
                          ) : (
                            <div className="flex items-center gap-2">
                              <span className="text-white/80">Total:</span>
                              <span className="font-bold text-xl">
                                ₹{cartTotals.subtotal.toFixed(2)}
                              </span>
                            </div>
                          )}
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Enhanced Cart Items */}
                  <ScrollArea className="flex-1 max-h-[40vh] md:max-h-[60vh] overflow-y-auto">
                    {cart.length === 0 ? (
                      <div className="p-12 text-center">
                        <div className="relative mb-6">
                          <div className="w-20 h-20 bg-gray-100 rounded-full mx-auto flex items-center justify-center">
                            <ShoppingBag size={32} className="text-gray-400" />
                          </div>
                          <div className="absolute -top-2 -right-2 w-8 h-8 bg-red-100 rounded-full flex items-center justify-center">
                            <span className="text-red-500 text-lg">😔</span>
                          </div>
                        </div>
                        <h3 className="text-gray-700 font-semibold mb-2">
                          Your cart is empty
                        </h3>
                        <p className="text-gray-500 text-sm">
                          Add some delicious items to get started
                        </p>
                      </div>
                    ) : (
                      <div className="p-4 space-y-3">
                        {cart.map((dish, dishIndex) => (
                          <div key={dishIndex} className="group">
                            <div className="bg-white rounded-2xl p-4 border border-gray-100 hover:border-gray-200 hover:shadow-lg transition-all duration-300 hover:scale-[1.01]">
                              <div className="flex items-start gap-4">
                                {/* Dish Info */}
                                <div className="flex-1 min-w-0">
                                  <div className="flex items-center gap-2 mb-2">
                                    <h4 className="font-semibold text-gray-900 text-base">
                                      {dish.name}
                                    </h4>
                                    {dish.isVeg && (
                                      <div className="h-4 w-4 border-2 border-green-600 rounded-sm flex items-center justify-center bg-white">
                                        <div className="h-2 w-2 bg-green-600 rounded-full"></div>
                                      </div>
                                    )}
                                  </div>

                                  {dish.description && (
                                    <p className="text-xs text-gray-600 mb-3 leading-relaxed line-clamp-2">
                                      {stringReducer(dish.description, 20)}
                                    </p>
                                  )}

                                  <div className="flex items-center justify-between">
                                    <Badge
                                      variant="outline"
                                      className="font-bold text-sm px-3 py-1 border-2"
                                      style={{
                                        color: theme.primaryColor,
                                        borderColor: `${theme.primaryColor}40`,
                                        backgroundColor: `${theme.primaryColor}08`,
                                      }}
                                    >
                                      ₹{dish.price}
                                    </Badge>

                                    <div className="text-xs text-gray-500">
                                      ₹
                                      {(
                                        dish.price * (dish.quantity || 1)
                                      ).toFixed(2)}{" "}
                                      total
                                    </div>
                                  </div>
                                </div>

                                {/* Enhanced Quantity Controls */}
                                <div className="flex items-center gap-1 bg-gray-50 rounded-2xl p-1">
                                  <Button
                                    onClick={() =>
                                      handleUpdateQuantity(dish, "decrease")
                                    }
                                    variant="ghost"
                                    size="sm"
                                    className="h-10 w-10 rounded-xl hover:bg-white transition-all duration-200 hover:scale-110 active:scale-95 shadow-sm"
                                    style={{ color: theme.primaryColor }}
                                  >
                                    <Minus size={16} />
                                  </Button>

                                  <div className="w-12 text-center">
                                    <span
                                      className="font-bold text-lg"
                                      style={{ color: theme.primaryColor }}
                                    >
                                      {dish.quantity || 1}
                                    </span>
                                  </div>

                                  <Button
                                    onClick={() =>
                                      handleUpdateQuantity(dish, "increase")
                                    }
                                    variant="ghost"
                                    size="sm"
                                    className="h-10 w-10 rounded-xl hover:bg-white transition-all duration-200 hover:scale-110 active:scale-95 shadow-sm"
                                    style={{ color: theme.primaryColor }}
                                  >
                                    <Plus size={16} />
                                  </Button>
                                </div>
                              </div>

                              {/* Subtle item separator with animation */}
                              <div
                                className="absolute bottom-0 left-4 right-4 h-0.5 bg-gradient-to-r opacity-0 group-hover:opacity-100 transition-opacity duration-300"
                                style={{
                                  background: `linear-gradient(to right, transparent, ${theme.primaryColor}40, transparent)`,
                                }}
                              />
                            </div>
                          </div>
                        ))}
                      </div>
                    )}
                  </ScrollArea>

                  {/* Enhanced Footer with Bill Breakdown */}
                  <div className="relative">
                    {/* Gradient overlay for smooth transition */}
                    <div className="absolute -top-6 left-0 right-0 h-6 bg-gradient-to-t from-gray-50 to-transparent pointer-events-none" />

                    <div className="p-4 border-t bg-gradient-to-br from-gray-50 to-white rounded-b-2xl">
                      {cart.length > 0 && (
                        <>
                          {/* Bill Summary */}
                          <div className="bg-white rounded-2xl p-4 mb-4 border border-gray-100 shadow-sm">
                            <div className="flex justify-between items-center mb-3">
                              <span className="text-gray-700 font-medium">
                                Subtotal
                              </span>
                              <div className="text-right">
                                {appliedOffers.length > 0 ? (
                                  <div className="flex items-center gap-2">
                                    <span className="line-through text-gray-400 text-sm">
                                      ₹{cartTotals.subtotal.toFixed(2)}
                                    </span>
                                    <span className="font-bold text-lg text-gray-900">
                                      ₹{cartTotals.finalTotal.toFixed(2)}
                                    </span>
                                  </div>
                                ) : (
                                  <span className="font-bold text-lg text-gray-900">
                                    ₹{cartTotals.subtotal.toFixed(2)}
                                  </span>
                                )}
                              </div>
                            </div>

                            {/* Enhanced Offers Breakdown */}
                            {appliedOffers.length > 0 && (
                              <div className="space-y-2 pt-2 border-t border-gray-100">
                                <div className="flex items-center gap-2 mb-2">
                                  <span className="text-green-600 text-sm">
                                    🎁
                                  </span>
                                  <span className="text-green-700 font-medium text-sm">
                                    Applied Offers:
                                  </span>
                                </div>
                                {appliedOffers.map((offer, index) => (
                                  <div
                                    key={index}
                                    className="flex justify-between items-center py-2 px-3 bg-green-50 rounded-lg border border-green-100"
                                  >
                                    <span className="text-green-700 text-sm font-medium">
                                      {(offer as any).offerName || offer.name}
                                    </span>
                                    <span className="text-green-600 font-bold">
                                      -₹
                                      {(offer as any).discountAmount?.toFixed(
                                        2
                                      ) || "0.00"}
                                    </span>
                                  </div>
                                ))}
                              </div>
                            )}
                          </div>

                          {/* Enhanced Checkout Button */}
                          <Button
                            className="w-full py-4 rounded-2xl font-bold text-white text-base transition-all duration-300 transform hover:scale-[1.02] active:scale-98 shadow-lg hover:shadow-xl flex items-center justify-center gap-3 relative overflow-hidden"
                            style={{ backgroundColor: theme.primaryColor }}
                            onClick={() =>
                              router.push(`/checkout${window.location.search}`)
                            }
                            disabled={cart.length === 0}
                          >
                            {/* Button background animation */}
                            <div className="absolute inset-0 bg-white/20 translate-x-[-100%] group-hover:translate-x-[100%] transition-transform duration-700 skew-x-12" />

                            <div className="relative z-10 flex items-center gap-3 text-lg">
                              <div className="p-1 bg-white/20 rounded-lg">
                                <ShoppingBag size={20} />
                              </div>
                              <span>Proceed to Checkout</span>
                            </div>
                          </Button>
                        </>
                      )}
                    </div>
                  </div>
                </div>
              </DropdownMenuContent>
            </DropdownMenu>

            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button
                  variant="ghost"
                  size="icon"
                  className="h-10 w-10 md:h-8 md:w-8 rounded-full mobile-touch-target"
                >
                  <Icon
                    icon="simple-line-icons:options-vertical"
                    width="20"
                    height="20"
                  />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent className="w-36">
                <DropdownMenuLabel>Settings</DropdownMenuLabel>
                <Separator />
                <DropdownMenuItem onClick={() => handleClearConversation()}>
                  <span className="flex items-center justify-between w-full">
                    Clear chat <Trash2 className="h-4 w-4" />
                  </span>
                </DropdownMenuItem>
                <DropdownMenuItem
                  onClick={async () => {
                    try {
                      const foodChainId = getFoodChainId();
                      const outletId = getOutletId();
                      if (!foodChainId || !outletId) {
                        toast.error(
                          "Food chain and outlet information is required"
                        );
                        return;
                      }
                      await clearCartAPI(foodChainId, outletId);
                      // Immediately refresh cart to reflect changes in UI
                      refreshCart(true);
                      toast.success("Cart cleared successfully");
                    } catch (error) {
                      console.error("Error clearing cart:", error);
                      toast.error("Failed to clear cart");
                    }
                  }}
                >
                  <span className="flex items-center justify-between w-full">
                    Clear cart <ShoppingCart className="h-4 w-4" />
                  </span>
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => router.push("/outlets")}>
                  <span className="flex items-center justify-between w-full">
                    Outlets <MapPin className="h-4 w-4" />
                  </span>
                </DropdownMenuItem>
                <DropdownMenuItem
                  onClick={() => {
                    localStorage.setItem("to-chat", "true");
                    router.push("/orders");
                  }}
                >
                  <span className="flex items-center justify-between w-full">
                    Orders <Clock className="h-4 w-4" />
                  </span>
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => logout()}>
                  <span className="flex items-center justify-between w-full">
                    Logout <LogOut className="h-4 w-4" />
                  </span>
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </div>
      </header>

      <div
        className="flex-1 overflow-hidden px-2 min-h-0 chat-area"
        ref={chatContainerRef}
      >
        <div className="bg-white border h-full rounded-2xl p-2 md:p-3 flex flex-col gap-1 md:gap-4 overflow-y-auto">
          {conversationHistory.map((conversation, index) => {
            const isUserSender = conversation.sender === "user";
            const currentTime = conversation.time
              ? new Date(conversation.time).getTime()
              : null;
            const previousTime =
              index > 0 && conversationHistory[index - 1]?.time
                ? new Date(conversationHistory[index - 1].time!).getTime()
                : null;
            // Calculate time difference in minutes
            const timeDifference =
              currentTime && previousTime
                ? (currentTime - previousTime) / (1000 * 60)
                : 0;

            // Show separator if time difference is more than 30 minutes (you can adjust this)
            const showSeparator = index > 0 && timeDifference > 60;

            // Helper function to format the separator date
            const formatSeparatorDate = (timestamp: number) => {
              const date = new Date(timestamp);
              const today = new Date();
              const yesterday = new Date(today);
              yesterday.setDate(yesterday.getDate() - 1);

              if (date.toDateString() === today.toDateString()) {
                return "Recent";
              } else if (date.toDateString() === yesterday.toDateString()) {
                return "Yesterday";
              } else {
                return date.toLocaleDateString("en-US", {
                  weekday: "long",
                  year: "numeric",
                  month: "long",
                  day: "numeric",
                });
              }
            };
            return (
              <div key={index}>
                {/* Time Separator */}
                {showSeparator && (
                  <div className="flex items-center justify-center my-6">
                    <div className="flex-grow h-px bg-gray-200"></div>
                    <div className="px-4 py-2 bg-gray-100 rounded-full text-xs text-gray-500 font-medium">
                      {currentTime && formatSeparatorDate(currentTime)}
                    </div>
                    <div className="flex-grow h-px bg-gray-200"></div>
                  </div>
                )}

                <div
                  className={`flex items-end fle relative ${
                    isUserSender ? "justify-end" : "justify-start"
                  } ${index == 0 ? `mt-4` : `mt-2`}`}
                >
                  <div
                    ref={
                      index === conversationHistory.length - 1
                        ? (el: HTMLDivElement | null) => {
                            lastMessageRef.current = el;
                          }
                        : undefined
                    }
                    data-last-message={
                      index === conversationHistory.length - 1
                        ? "true"
                        : undefined
                    }
                    className={`relative max-w-[85%] md:max-w-4/5 p-2 md:p-3 rounded-lg shadow-md text-xs md:text-sm min-w-[50px] ${
                      !isUserSender
                        ? ` text-white rounded-bl-none`
                        : " text-black rounded-br-none"
                    }`}
                    style={{
                      background: isUserSender ? "#d0d0d0" : theme.primaryColor,
                    }}
                    id="streamingText"
                  >
                    <div
                      className={`text-xs text-gray-400 absolute top-[-1rem] ${
                        isUserSender ? "right-0" : "left-0"
                      }`}
                    >
                      {conversation.time
                        ? formatTime(new Date(conversation.time).getTime())
                        : ""}
                      {/* Typing animation for AI messages (non-streaming) */}
                      {conversation.sender !== "user" &&
                        responseLoading &&
                        index === conversationHistory.length - 1 && (
                          <div className="mt-2 inline-flex items-center space-x-1">
                            <span
                              className="inline-block h-2 w-2 rounded-full bg-gray-400 animate-bounce"
                              style={{ animationDelay: "0ms" }}
                            />
                            <span
                              className="inline-block h-2 w-2 rounded-full bg-gray-400 animate-bounce"
                              style={{ animationDelay: "150ms" }}
                            />
                            <span
                              className="inline-block h-2 w-2 rounded-full bg-gray-400 animate-bounce"
                              style={{ animationDelay: "300ms" }}
                            />
                          </div>
                        )}
                    </div>
                    {!isUserSender ? (
                      <StreamingText
                        text={formatStringToHtml(conversation.message)}
                        isStreaming={
                          responseLoading &&
                          index === conversationHistory.length - 1
                        }
                        className="streaming-message-content"
                      />
                    ) : (
                      <div
                        dangerouslySetInnerHTML={{
                          __html: formatStringToHtml(conversation.message),
                        }}
                      ></div>
                    )}
                    {!isUserSender && (
                      <TextToSpeech
                        text={conversation.message}
                        options={{
                          voice: "Google US English",
                        }}
                      />
                    )}
                  </div>
                </div>

                {/* Display FAQ suggestions if available */}
                {!isUserSender &&
                  conversation.suggestedQuestions &&
                  conversation.suggestedQuestions.length > 0 && (
                    <div className="mt-4 mb-4 ml-4">
                      <div className="flex items-center text-xs md:text-sm text-gray-600 mb-2">
                        <Icon
                          icon="carbon:help"
                          className="mr-2"
                          width="16"
                          height="16"
                        />
                        <span className="font-medium">Suggested Questions</span>
                      </div>
                      <div className="flex flex-wrap gap-2">
                        {conversation.suggestedQuestions.map(
                          (question, qIndex) => (
                            <div
                              key={qIndex}
                              className="cursor-pointer px-3 py-2 bg-gray-50 border border-gray-200 rounded-full text-xs md:text-sm hover:bg-blue-50 hover:border-blue-200 transition-all"
                              style={{ color: theme.secondaryColor }}
                              onClick={() => {
                                setUserMessage(question);
                                handleNewMessage(question);
                              }}
                            >
                              {question}
                            </div>
                          )
                        )}
                      </div>
                    </div>
                  )}
                {conversation.dishes && conversation.dishes.length > 0 && (
                  <div className="mt-4 mb-4 ml-4 w-[90%]">
                    <div className="flex items-center text-xs md:text-sm text-gray-600 mb-2">
                      <Icon
                        icon="carbon:restaurant"
                        className="mr-2"
                        width="16"
                        height="16"
                      />
                      <span className="font-medium">Recommended Dishes</span>
                    </div>
                    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3 mt-3 p-2 md:p-4">
                      {conversation.dishes.map((dish, dishIndex) => (
                        <div
                          key={dishIndex}
                          className={`bg-white border relative border-${
                            dish?.isFeatured ? "yellow" : "gray"
                          }-200 rounded-lg shadow-sm hover:shadow-md transition-all overflow-hidden flex flex-col`}
                        >
                          {dish.image && (
                            <div className="h-32 w-full overflow-hidden relative">
                              <Image
                                src={dish.image}
                                alt={dish.name}
                                fill
                                sizes="(max-width: 768px) 100vw, 33vw"
                                className="object-cover"
                                style={{ objectFit: "cover" }}
                              />
                            </div>
                          )}
                          <div className="p-3 flex flex-col flex-grow">
                            <div className="flex justify-between items-start mb-1">
                              <h3
                                className="font-medium text-xs md:text-sm flex items-center gap-1"
                                style={{ color: theme.secondaryColor }}
                              >
                                {dish?.isFeatured && (
                                  <div className="">
                                    <Icon
                                      icon="mdi:star"
                                      width="20"
                                      height="20"
                                      className="text-yellow-500"
                                    />
                                  </div>
                                )}
                                {dish.name}
                              </h3>
                              <div className="flex items-center gap-1">
                                {dish.isVeg !== undefined ? (
                                  dish.isVeg ? (
                                    <div className="h-4 w-4 border border-green-600 flex items-center justify-center flex-shrink-0 ml-1">
                                      <div className="h-2 w-2 bg-green-600 rounded-full"></div>
                                    </div>
                                  ) : (
                                    <div className="h-4 w-4 border border-red-600 flex items-center justify-center flex-shrink-0 ml-1">
                                      <div className="h-2 w-2 bg-red-600 rounded-full"></div>
                                    </div>
                                  )
                                ) : null}
                              </div>
                            </div>

                            {dish.description && (
                              <p className="text-xs text-gray-500 mb-2 line-clamp-2">
                                {dish.description}
                              </p>
                            )}

                            <div className="mt-auto flex items-center justify-between">
                              <Badge
                                variant="outline"
                                className="font-bold"
                                style={{
                                  color: theme.primaryColor,
                                  borderColor: theme.primaryColor,
                                }}
                              >
                                ₹{dish.price}
                              </Badge>

                              <Button
                                onClick={() => handleAddToCart(dish)}
                                variant="ghost"
                                size="sm"
                                className="h-8 rounded-full flex items-center justify-center transition-colors bg-gray-100 hover:bg-gray-200 ml-2"
                                style={{ color: theme.primaryColor }}
                              >
                                {isItemInCart(dish._id!) ? (
                                  <div className="flex items-center">
                                    <Minus size={14} className="mr-1" />
                                    <span className="text-xs">Remove</span>
                                  </div>
                                ) : (
                                  <div className="flex items-center">
                                    <Plus size={14} className="mr-1" />
                                    <span className="text-xs">Add</span>
                                  </div>
                                )}
                              </Button>
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            );
          })}
          {responseLoading && (
            <div
              style={{
                background: theme.primaryColor + "20",
                borderLeft: `4px solid ${theme.primaryColor}`,
                boxShadow: "0 2px 5px rgba(0,0,0,0.1)",
              }}
              className="text-center p-3 rounded-xl mt-4 flex items-center justify-center"
            >
              <div className="flex items-center space-x-2">
                <div className="flex space-x-1">
                  {[0, 1, 2].map((dot) => (
                    <div
                      key={dot}
                      className="h-2 w-2 rounded-full"
                      style={{
                        background: theme.primaryColor,
                        animation: `pulse 1.5s infinite ease-in-out ${
                          dot * 0.2
                        }s`,
                      }}
                    />
                  ))}
                </div>
                <span className="font-medium">Cooking your response...</span>
              </div>
              <style jsx>{`
                @keyframes pulse {
                  0%,
                  100% {
                    transform: scale(1);
                    opacity: 1;
                  }
                  50% {
                    transform: scale(1.2);
                    opacity: 0.7;
                  }
                }
              `}</style>
            </div>
          )}
        </div>
      </div>
      <div className="sticky h-24 bottom-4 bg-white border-t shadow-lg input-container">
        <div className="p-3 md:p-4 max-w-4xl mx-auto">
          <div className="relative flex items-center gap-2 md:gap-3">
            {/* Input Field */}
            <div className="flex-1 relative">
              <Input
                className="w-full h-12 md:h-14 pl-4 pr-12 md:pr-14 rounded-2xl border-2 border-gray-200 focus:border-blue-500 focus:ring-2 focus:ring-blue-100 transition-all duration-200 text-base bg-gray-50 focus:bg-white shadow-sm"
                placeholder={
                  conversationHistory.at(-1)?.suggestedQuestions?.[0] ||
                  placeHolder
                }
                autoFocus={userMessage.trim() === ""}
                onKeyDown={handleKeyPress}
                onFocus={handleInputFocus}
                value={userMessage}
                onChange={(e) => setUserMessage(e.target.value)}
                style={{
                  fontSize: "16px", // Prevents zoom on iOS
                }}
              />

              {/* Clear Button */}
              {userMessage.trim() !== "" && !responseLoading && (
                <button
                  className="absolute right-3 top-1/2 transform -translate-y-1/2 p-1 rounded-full hover:bg-gray-100 transition-colors"
                  onClick={() => setUserMessage("")}
                  type="button"
                >
                  <Icon
                    icon="iconoir:cancel"
                    width="18"
                    height="18"
                    className="text-gray-400"
                  />
                </button>
              )}
            </div>

            {/* Voice Input Button */}
            <MobileVoiceInput
              onTranscription={(text) => {
                setUserMessage(text);
                console.log("✅ Voice transcription completed:", text);
              }}
              language={language}
              disabled={responseLoading}
              className="flex-shrink-0"
            />

            {/* Send Button */}
            <Button
              className={`send-button h-12 w-12 md:h-14 md:w-14 rounded-2xl shadow-lg ${
                userMessage.trim() === "" || responseLoading
                  ? "opacity-50 cursor-not-allowed"
                  : ""
              }`}
              style={{
                backgroundColor: theme.primaryColor,
                border: `2px solid ${theme.primaryColor}`,
              }}
              onClick={() => handleButtonClick()}
              disabled={userMessage.trim() === "" || responseLoading}
              type="button"
            >
              {responseLoading ? (
                <div className="animate-spin">
                  <Icon
                    icon="eos-icons:loading"
                    width="24"
                    height="24"
                    className="text-white"
                  />
                </div>
              ) : (
                <Icon
                  icon="fluent:send-32-regular"
                  width="24"
                  height="24"
                  className="text-white ml-0.5"
                />
              )}
            </Button>
          </div>
        </div>

        {/* Menu Button */}
        <div
          className={`fixed md:bottom-24 bottom-24
            ${isOpen ? "2" : "20"} 
          right-0 z-50`}
        >
          <DropdownMenu
            open={isOpen}
            onOpenChange={(open) => {
              setIsOpen(open);
              if (open) setIsMenuClicked(true);
            }}
          >
            <DropdownMenuTrigger asChild onClick={() => setIsOpen(!isOpen)}>
              <div
                className={`relative flex justify-center items-center rounded-bl-md rounded-tl-md h-10 w-10 md:h-10 md:w-10 cursor-pointer shadow-lg transition-all duration-800 hover:shadow-xl ${
                  !isMenuClicked ? "animate-pulse" : ""
                } ${isOpen ? "scale-110" : "scale-100"}`}
                style={{
                  backgroundColor: theme.primaryColor,
                  border: `2px solid ${theme.primaryColor}`,
                  boxShadow: `0 0 10px ${theme.primaryColor}40`,
                }}
              >
                {isOpen ? (
                  <X className="text-white" />
                ) : (
                  <>
                    <Icon
                      icon="ion:restaurant-outline"
                      width="22"
                      height="22"
                      className="text-white"
                    />
                    <div className="absolute -top-2 -right-2 bg-red-500 text-white text-xs rounded-full h-6 w-6 flex items-center justify-center font-medium">
                      {menu.reduce(
                        (acc, category) =>
                          acc +
                          (category.category === "Featured"
                            ? 0
                            : category.dishes.length),
                        0
                      )}
                    </div>
                  </>
                )}
              </div>
            </DropdownMenuTrigger>

            <DropdownMenuContent
              className="w-[95vw] max-w-[400px] md:w-80 p-0 rounded-lg border-0 shadow-2xl mr-2 md:mr-6"
              sideOffset={5}
              align="end"
            >
              <div className="flex flex-col h-full">
                {/* Enhanced Header */}
                <div className="relative overflow-hidden">
                  <div
                    className="bg-gradient-to-br p-4 pb-6 relative"
                    style={{
                      backgroundImage: `linear-gradient(135deg, ${theme.primaryColor}ee, ${theme.primaryColor}cc, ${theme.primaryColor}99)`,
                    }}
                  >
                    {/* Decorative background elements */}
                    <div className="absolute top-0 right-0 w-32 h-32 bg-white/10 rounded-full -translate-y-16 translate-x-16" />
                    <div className="absolute bottom-0 left-0 w-24 h-24 bg-white/5 rounded-full translate-y-12 -translate-x-12" />

                    <div className="relative z-10">
                      <DropdownMenuLabel className="text-white text-xl font-bold mb-1">
                        Our Menu
                      </DropdownMenuLabel>
                      <p className="text-white/80 text-sm">
                        Select items to order •{" "}
                        {menu.reduce(
                          (total, cat) => total + cat.dishes.length,
                          0
                        )}{" "}
                        delicious options
                      </p>
                    </div>
                  </div>
                </div>

                {/* Offers Carousel */}
                <div className="px-4 py-3 bg-gradient-to-b from-gray-50 to-white">
                  <OffersCarousel
                    outletId={outletId}
                    foodChainId={foodChainId}
                    onOfferClick={(offer) => {
                      console.log("Offer clicked:", offer);
                      // You can add offer application logic here
                    }}
                  />
                </div>

                {/* Enhanced Category Navigation */}
                <div className="px-4 py-3 bg-white border-b border-gray-100">
                  <div className="flex gap-2 overflow-x-auto scrollbar-hide pb-2">
                    <style jsx>{`
                      .scrollbar-hide::-webkit-scrollbar {
                        display: none;
                      }
                    `}</style>

                    {menu.map((category, index) => {
                      const isActive = selectedCategory === category.category;
                      return (
                        <button
                          key={index}
                          onClick={() => {
                            handleCategorySelect(
                              category.category === selectedCategory
                                ? ""
                                : category.category
                            );
                          }}
                          className={`flex-shrink-0 px-4 py-2.5 rounded-full text-sm font-medium transition-all duration-300 transform ${
                            isActive
                              ? "text-white shadow-lg scale-105"
                              : "bg-gray-100 text-gray-700 hover:bg-gray-200 hover:scale-102 active:scale-95"
                          }`}
                          style={{
                            backgroundColor: isActive
                              ? theme.primaryColor
                              : undefined,
                          }}
                        >
                          <span>{category.category}</span>
                          <span className="ml-2 text-xs opacity-75 bg-white/20 rounded-full px-2 py-0.5">
                            {category.dishes.length}
                          </span>
                        </button>
                      );
                    })}
                  </div>
                </div>

                {/* Enhanced Menu Items */}
                <ScrollArea className="flex-1 overflow-y-scroll px-4">
                  <div className="py-2">
                    {menu.map((category, categoryIndex) => (
                      <div
                        key={categoryIndex}
                        id={`category-${category.category}`}
                        className={`mb-6 transition-all duration-300 ${
                          selectedCategory &&
                          selectedCategory !== category.category
                            ? "opacity-40 scale-98 blur-[1px]"
                            : ""
                        }`}
                        onClick={() => {
                          if (selectedCategory !== category.category) {
                            handleCategorySelect("");
                          }
                        }}
                      >
                        {/* Enhanced Category Header */}
                        <div className="sticky top-0 bg-white/95 backdrop-blur-sm z-10 py-3 mb-4 border-b border-gray-100 rounded-lg">
                          <DropdownMenuLabel
                            className="text-lg font-bold px-0 py-0 flex justify-between items-center"
                            id={category.category}
                          >
                            <div className="flex items-center gap-2">
                              <div
                                className="w-1 h-6 rounded-full"
                                style={{ backgroundColor: theme.primaryColor }}
                              />
                              <span className="text-gray-900">
                                {category.category}
                              </span>
                            </div>
                            <div className="flex items-center gap-2">
                              <Badge
                                variant="secondary"
                                className="text-xs font-medium bg-gray-100 text-gray-600"
                              >
                                {category.dishes.length} items
                              </Badge>
                            </div>
                          </DropdownMenuLabel>
                        </div>

                        {/* Enhanced Dish Cards */}
                        <div className="space-y-3">
                          {category.dishes.map((dish, dishIndex) => (
                            <div key={dishIndex} className="relative group">
                              <div className="bg-white rounded-2xl p-4 shadow-sm border border-gray-100 hover:shadow-lg hover:border-gray-200 transition-all duration-300 hover:scale-[1.02] active:scale-[0.98]">
                                <div className="flex justify-between items-start gap-3">
                                  {/* Dish Information */}
                                  <div className="flex-1 min-w-0">
                                    <div className="flex items-start gap-2 mb-2">
                                      <div className="font-semibold text-gray-900 text-base leading-tight">
                                        {dish.name}
                                      </div>
                                      {dish?.isFeatured && (
                                        <div className="flex-shrink-0 bg-yellow-100 rounded-full p-1">
                                          <span className="text-yellow-600 text-xs">
                                            ⭐
                                          </span>
                                        </div>
                                      )}
                                    </div>

                                    {dish.description && (
                                      <div className="text-sm text-gray-600 mb-3 leading-relaxed">
                                        {stringReducer(dish.description, 100)}
                                      </div>
                                    )}

                                    <div className="flex items-center justify-between">
                                      <Badge
                                        variant="outline"
                                        className="font-bold text-base px-3 py-1 border-2"
                                        style={{
                                          color: theme.primaryColor,
                                          borderColor: `${theme.primaryColor}40`,
                                          backgroundColor: `${theme.primaryColor}08`,
                                        }}
                                      >
                                        ₹{dish.price}
                                      </Badge>
                                    </div>
                                  </div>

                                  {/* Enhanced Add Button */}
                                  <div className="flex-shrink-0">
                                    <Button
                                      onClick={(e) => {
                                        e.stopPropagation();
                                        handleAddToCart(dish);
                                      }}
                                      variant="ghost"
                                      className={`h-12 w-12 rounded-2xl transition-all duration-300 transform hover:scale-110 active:scale-95 shadow-md hover:shadow-lg ${
                                        isItemInCart(dish._id!)
                                          ? "bg-red-50 hover:bg-red-100 border-2 border-red-200"
                                          : "hover:shadow-xl"
                                      }`}
                                      style={{
                                        backgroundColor: isItemInCart(dish._id!)
                                          ? undefined
                                          : `${theme.primaryColor}15`,
                                        borderColor: isItemInCart(dish._id!)
                                          ? undefined
                                          : `${theme.primaryColor}30`,
                                        color: isItemInCart(dish._id!)
                                          ? "#ef4444"
                                          : theme.primaryColor,
                                      }}
                                    >
                                      {isItemInCart(dish._id!) ? (
                                        <Minus size={20} />
                                      ) : (
                                        <Plus size={20} />
                                      )}
                                    </Button>
                                  </div>
                                </div>

                                {/* Subtle hover indicator */}
                                <div
                                  className="absolute bottom-0 left-0 right-0 h-1 rounded-b-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-300"
                                  style={{
                                    backgroundColor: `${theme.primaryColor}40`,
                                  }}
                                />
                              </div>
                            </div>
                          ))}
                        </div>
                      </div>
                    ))}
                  </div>
                </ScrollArea>

                {/* Enhanced Footer */}
                <div className="relative">
                  {/* Gradient overlay for smooth transition */}
                  <div className="absolute -top-6 left-0 right-0 h-6 bg-gradient-to-t from-gray-50 to-transparent pointer-events-none" />

                  <div className="p-4 border-t bg-gradient-to-r from-gray-50 to-white rounded-b-lg">
                    <button
                      className="w-full py-3.5 rounded-2xl font-semibold text-white text-base transition-all duration-300 transform hover:scale-[1.02] active:scale-98 shadow-lg hover:shadow-xl flex items-center justify-center gap-2"
                      style={{ backgroundColor: theme.primaryColor }}
                      onClick={() => {
                        setIsOpen(false);
                        setIsCartMenuOpen(true);
                      }}
                    >
                      <span>View Cart</span>
                      <ShoppingCart size={20} />
                    </button>
                  </div>
                </div>
              </div>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>
    </div>
  );
};

const ChatPage = () => {
  return (
    <Suspense>
      <Page />
    </Suspense>
  );
};

export default ChatPage;
