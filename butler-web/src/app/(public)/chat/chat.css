/* Mobile viewport handling with dynamic viewport units */
:root {
  --vh: 1vh;
  --app-height: 100vh;
  --safe-area-top: env(safe-area-inset-top, 0px);
  --safe-area-bottom: env(safe-area-inset-bottom, 0px);
  --safe-area-left: env(safe-area-inset-left, 0px);
  --safe-area-right: env(safe-area-inset-right, 0px);
}

/* Prevent zoom on input focus on iOS */
input[type="text"],
input[type="email"],
input[type="password"],
input[type="search"],
input[type="tel"],
input[type="url"],
textarea,
select {
  font-size: 16px !important;
}

/* Main container styles with improved viewport handling */
.main-container {
  position: relative;
  width: 100%;
  height: 100vh;
  height: 100dvh; /* Dynamic viewport height */
  height: var(--app-height);
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

/* Input container improvements */
.input-container {
  position: sticky;
  bottom: 0;
  left: 0;
  right: 0;
  background: white;
  z-index: 50;
  border-top: 1px solid #e5e7eb;
  box-shadow: 0 -4px 6px -1px rgba(0, 0, 0, 0.1);
  flex-shrink: 0;
}

/* Mobile specific fixes with enhanced viewport handling */
@media screen and (max-width: 768px) {
  html {
    height: 100%;
    height: 100dvh;
    overflow: hidden;
    /* Prevent elastic scrolling on iOS */
    overscroll-behavior: none;
  }

  body {
    height: 100%;
    height: 100dvh;
    height: var(--app-height);
    overflow: hidden;
    position: fixed;
    width: 100%;
    /* Prevent elastic scrolling on iOS */
    overscroll-behavior: none;
    /* Add safe area padding */
    padding-top: var(--safe-area-top);
    padding-left: var(--safe-area-left);
    padding-right: var(--safe-area-right);
  }

  .main-container {
    height: 100vh;
    height: 100dvh;
    height: var(--app-height);
    max-height: 100vh;
    max-height: 100dvh;
    max-height: var(--app-height);
    overflow: hidden;
    display: flex;
    flex-direction: column;
    /* Account for safe areas */
    min-height: calc(100vh - var(--safe-area-top) - var(--safe-area-bottom));
    min-height: calc(100dvh - var(--safe-area-top) - var(--safe-area-bottom));
  }

  /* Chat area should be scrollable */
  .chat-area {
    flex: 1;
    overflow-y: auto;
    -webkit-overflow-scrolling: touch;
    /* Prevent overscroll bounce */
    overscroll-behavior: contain;
    /* Add bottom padding to prevent content from being hidden behind input */
    padding-bottom: calc(100px + var(--safe-area-bottom));
  }

  /* Input area fixed at bottom */
  .input-container {
    flex-shrink: 0;
    position: relative;
    bottom: 0;
    padding-bottom: var(--safe-area-bottom);
    /* Ensure it stays above safe area */
    margin-bottom: 0;
  }

  /* Prevent body scroll when keyboard is open */
  body.keyboard-open {
    position: fixed;
    width: 100%;
    height: 100%;
    height: var(--app-height);
    overflow: hidden;
  }

  /* Ensure proper height calculation when keyboard is open */
  .main-container.keyboard-open {
    height: 100vh;
    height: 100dvh;
    height: var(--app-height);
    max-height: 100vh;
    max-height: 100dvh;
    max-height: var(--app-height);
  }
}

/* Enhanced input styling */
.input-container input {
  transition: all 0.2s ease-in-out;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.input-container input:focus {
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.15);
  transform: translateY(-1px);
}

/* Send button animations */
.send-button {
  transition: all 0.2s ease-in-out;
}

.send-button:hover:not(:disabled) {
  transform: translateY(-1px);
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.2);
}

.send-button:active:not(:disabled) {
  transform: translateY(0);
}

/* Loading animation for send button */
@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.animate-spin {
  animation: spin 1s linear infinite;
}

/* Typing indicator animation */
@keyframes typing-dot {
  0%, 60%, 100% {
    transform: translateY(0);
    opacity: 0.4;
  }
  30% {
    transform: translateY(-8px);
    opacity: 1;
  }
}

.typing-dot {
  animation: typing-dot 1.4s infinite ease-in-out;
}

.typing-dot:nth-child(1) { animation-delay: 0s; }
.typing-dot:nth-child(2) { animation-delay: 0.2s; }
.typing-dot:nth-child(3) { animation-delay: 0.4s; }

/* Responsive dropdown positioning */
@media (max-width: 768px) {
  .dropdown-content {
    position: fixed !important;
    top: auto !important;
    bottom: 80px !important;
    left: 8px !important;
    right: 8px !important;
    width: calc(100vw - 16px) !important;
    max-width: none !important;
    transform: none !important;
  }
}

#streamingText {
  display: inline-block;
}

@keyframes bounceIn {
  0% {
    transform: translateY(30px);
    opacity: 1;
  }
  80% {
    transform: translateY(-10px);
  }
  100% {
    transform: translateY(0);
  }
}

#streamingText {
  animation: bounceIn 0.4s ease-in-out forwards; /* Bounce effect with a smooth ease-out */
}

/* Mobile-specific improvements */
@media (max-width: 640px) {
  /* Ensure header stays visible */
  .chat-header {
    position: sticky;
    top: 0;
    z-index: 50;
  }

  /* Improve touch targets */
  .mobile-touch-target {
    min-height: 44px;
    min-width: 44px;
  }

  /* Better spacing for mobile */
  .mobile-padding {
    padding: 8px;
  }

  /* Responsive text sizing */
  .mobile-text {
    font-size: 14px;
  }
}

/* Typewriter animation styles */
.typewriter-container {
  display: inline-block;
}

.typewriter-container p {
  margin-bottom: 0.5rem;
  display: inline;
}

.typewriter-container p:last-child {
  margin-bottom: 0;
}

.typewriter-cursor {
  color: #9CA3AF;
  font-weight: normal;
  margin-left: 2px;
  animation: cursor-blink 1s infinite;
}

/* Smooth cursor animation */
@keyframes cursor-blink {
  0%, 50% { opacity: 1; }
  51%, 100% { opacity: 0; }
}

/* Streaming message content */
.streaming-message-content {
  display: inline-block;
  min-height: 1.2em;
}

/* Additional mobile browser UI handling */
@supports (height: 100dvh) {
  .main-container {
    height: 100dvh;
  }

  @media screen and (max-width: 768px) {
    body {
      height: 100dvh;
    }

    .main-container {
      height: 100dvh;
      max-height: 100dvh;
    }
  }
}

/* Fallback for browsers without dvh support */
@supports not (height: 100dvh) {
  .main-container {
    height: var(--app-height);
  }

  @media screen and (max-width: 768px) {
    body {
      height: var(--app-height);
    }

    .main-container {
      height: var(--app-height);
      max-height: var(--app-height);
    }
  }
}

/* Ensure proper stacking and positioning */
.chat-page {
  position: relative;
  isolation: isolate;
}

/* Enhanced safe area handling for notched devices */
@media screen and (max-width: 768px) {
  .chat-header {
    padding-top: max(var(--safe-area-top), 8px);
  }

  .input-container {
    padding-bottom: max(var(--safe-area-bottom), 8px);
    /* Ensure input is always above browser UI */
    transform: translateZ(0);
    will-change: transform;
    /* Force hardware acceleration */
    backface-visibility: hidden;
    /* Ensure it's always visible above browser UI */
    min-height: 80px;
  }

  /* Specific handling for browser toolbar overlap */
  .input-container {
    /* Use position fixed on mobile to avoid browser UI interference */
    position: fixed !important;
    bottom: 0 !important;
    left: 0 !important;
    right: 0 !important;
    width: 100% !important;
    /* Ensure it's above everything */
    z-index: 9999 !important;
  }

  /* Adjust chat area to account for fixed input */
  .chat-area {
    /* Add extra bottom padding to account for fixed input */
    padding-bottom: calc(120px + var(--safe-area-bottom)) !important;
  }

  /* Handle keyboard open state */
  body.keyboard-open .input-container {
    position: fixed !important;
    bottom: 0 !important;
    /* Ensure it stays visible when keyboard is open */
    transform: translateY(0) !important;
  }
}
  