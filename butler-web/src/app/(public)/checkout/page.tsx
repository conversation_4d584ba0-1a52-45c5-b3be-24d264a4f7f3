"use client";
// Removed unused eslint-disable directive
import useBackendCart from "@/hooks/useBackendCart";
import React, {
  Suspense,
  useState,
  useEffect,
  // useMemo, // Unused for now
  // useCallback, // Unused for now
} from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { useTheme } from "@/contexts/ThemeContext";
import { useRouter, useSearchParams } from "next/navigation";
import {
  Card,
  CardContent,
  CardTitle,
} from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";
import { Badge } from "@/components/ui/badge";
import { toast } from "sonner";
import {
  ArrowLeft,
  ShoppingBag,
  AlertCircle,
  Edit3,
  Tag,
  X,
  Loader2,
} from "lucide-react";
import { useSocket } from "@/contexts/SocketContext";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Checkbox } from "@/components/ui/checkbox";
// import ApplicableOffers from "@/components/custom/offers/ApplicableOffers"; // Temporarily disabled
import { Dish } from "@/app/type";
// import {
//   calculateTotalWithOffers,
//   CartItem,
//   OfferCalculationResult,
// } from "@/utils/offerCalculations"; // Unused for now

const CheckoutPageWrapper = () => {
  return (
    <Suspense>
      {" "}
      <UserCheckoutPage />
    </Suspense>
  );
};

const UserCheckoutPage = () => {
  const {
    cart,
    appliedOffers,
    cartTotals,
    // updateItemQuantity, // Unused for now
    removeFromCart: removeFromCartBackend,
    addToCart: addToCartBackend,
    clearCart,
    // refreshCart, // Unused for now
  } = useBackendCart();
  const [pageLoading, setPageLoading] = useState(true);
  const params = useSearchParams();
  const { theme } = useTheme();
  const router = useRouter();
  const [loading, setLoading] = useState(false);
  const [couponCode, setCouponCode] = useState("");
  const [couponLoading, setCouponLoading] = useState(false);
  const [appliedCoupon, setAppliedCoupon] = useState<{
    code: string;
    discount: number;
    discountType: string;
    discountValue: number;
  } | null>(null);

  // Addon editing state
  const [editingItem, setEditingItem] = useState<Dish | null>(null);
  const [isAddonDialogOpen, setIsAddonDialogOpen] = useState(false);
  const [selectedAddOns, setSelectedAddOns] = useState<
    Array<{
      addOnId: string;
      name: string;
      price: number;
      quantity: number;
    }>
  >([]);

  useEffect(() => {
    if (cart || cartTotals) setPageLoading(false);
  }, [cart, cartTotals]);
  // Handle addon editing
  const handleEditAddons = (item: Dish) => {
    setEditingItem(item);
    setSelectedAddOns(item.selectedAddOns || []);
    setIsAddonDialogOpen(true);
  };

  const handleSaveAddons = async () => {
    if (!editingItem) return;

    try {
      // Remove the existing item and re-add with new addons
      await removeFromCartBackend(editingItem._id!);

      // Format addons for backend
      const formattedAddOns = selectedAddOns.map((addon) => ({
        addOnId: addon.addOnId,
        name: addon.name,
        price: addon.price,
        quantity: addon.quantity,
      }));

      // Re-add item with new addons
      await addToCartBackend(
        editingItem,
        editingItem.quantity,
        formattedAddOns
      );

      setIsAddonDialogOpen(false);
      setEditingItem(null);
      setSelectedAddOns([]);
      toast.success("Add-ons updated successfully");
    } catch (error) {
      console.error("Error updating addons:", error);
      toast.error("Failed to update add-ons");
    }
  };

  const { customerSocket } = useSocket();

  // Check if we're updating an existing order
  const orderId = params?.get("orderId");
  const isUpdatingOrder = !!orderId;

  // Fix mobile scrolling issues
  useEffect(() => {
    let timeoutId: NodeJS.Timeout;

    const setViewportHeight = () => {
      // Clear any existing timeout to prevent memory leaks
      if (timeoutId) {
        clearTimeout(timeoutId);
      }

      // Set CSS custom properties for viewport height
      const vh = window.innerHeight * 0.01;
      document.documentElement.style.setProperty("--vh", `${vh}px`);
      document.documentElement.style.setProperty(
        "--app-height",
        `${window.innerHeight}px`
      );
    };

    const handleResize = () => {
      // Debounce resize events to prevent excessive calls
      if (timeoutId) {
        clearTimeout(timeoutId);
      }
      timeoutId = setTimeout(setViewportHeight, 100);
    };

    const handleOrientationChange = () => {
      // Delay to ensure proper viewport calculation after orientation change
      if (timeoutId) {
        clearTimeout(timeoutId);
      }
      timeoutId = setTimeout(setViewportHeight, 200);
    };

    // Force body to be scrollable immediately
    const forceScrollable = () => {
      if (typeof window !== "undefined") {
        // Remove any conflicting classes
        document.body.classList.remove("chat-page-body");

        // Force scrollable styles
        document.body.style.overflow = "auto";
        document.body.style.height = "auto";
        document.body.style.minHeight = "100vh";
        document.body.style.position = "static";
        document.documentElement.style.overflow = "auto";
        document.documentElement.style.height = "auto";
        document.documentElement.style.minHeight = "100vh";

        // Force a reflow to ensure styles are applied
        void document.body.offsetHeight;
      }
    };

    // Apply scrollable styles immediately
    forceScrollable();

    // Set initial viewport height
    setViewportHeight();

    // Add event listeners with passive option for better performance
    window.addEventListener("resize", handleResize, { passive: true });
    window.addEventListener("orientationchange", handleOrientationChange, {
      passive: true,
    });

    // Additional check after a short delay to ensure everything is applied
    const finalCheck = setTimeout(forceScrollable, 100);

    return () => {
      // Clean up all timeouts and event listeners
      if (timeoutId) {
        clearTimeout(timeoutId);
      }
      clearTimeout(finalCheck);
      window.removeEventListener("resize", handleResize);
      window.removeEventListener("orientationchange", handleOrientationChange);
    };
  }, []);

  // Get user data from localStorage if available
  const getUserData = () => {
    if (typeof window !== "undefined") {
      const userData = localStorage.getItem("user-data");
      return userData ? JSON.parse(userData) : null;
    }
    return null;
  };

  const userData = getUserData();

  // Form data with user information if available
  const [formData, setFormData] = useState({
    name: userData?.name || "",
    phone: userData?.phone || "",
    email: userData?.email || "",
    address: userData?.address || "",
    paymentMethod: "cash",
    specialInstructions: "",
    tableNumber: "",
  });

  // Use backend cart totals
  const subtotal = cartTotals.subtotal;
  // Use backend cart offer calculations
  const offerDiscount = cartTotals.totalOfferDiscount;

  // Calculate total with correct discount order: offers first, then coupons
  const amountAfterOffers = Math.max(0, subtotal - offerDiscount);
  const couponDiscount = appliedCoupon ? appliedCoupon.discount : 0;
  const total = Math.max(0, amountAfterOffers - couponDiscount);

  // Memoize orderData and appliedOfferIds to prevent unnecessary re-renders (temporarily disabled)
  // const orderData = useMemo(
  //   () => ({
  //     outletId: params?.get("outletId") || "",
  //     orderAmount: subtotal,
  //     customerId: localStorage.getItem("user-id") || undefined,
  //     items: cart.map((item) => ({
  //       dishId: item._id,
  //       quantity: item.quantity || 1,
  //       price: item.price,
  //       dishName: item.name,
  //     })),
  //     foodChainId: params?.get("chainId") || undefined,
  //   }),
  //   [params, subtotal, cart]
  // );

  // const appliedOfferIds = useMemo(
  //   () => appliedOffers.map((offer) => offer._id || ""),
  //   [appliedOffers]
  // );

  // Note: Offers are now handled automatically by the backend cart system

  // Handle coupon application
  const handleApplyCoupon = async () => {
    if (!couponCode.trim()) {
      toast.error("Please enter a coupon code");
      return;
    }

    setCouponLoading(true);
    try {
      const response = await fetch(
        `${process.env.NEXT_PUBLIC_BASE_URL}/api/v1/user/coupons/validate`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${localStorage.getItem("user-token")}`,
          },
          body: JSON.stringify({
            code: couponCode,
            outletId: params?.get("outletId") || "",
            amount: subtotal - offerDiscount,
          }),
        }
      );

      const result = await response.json();

      if (result.success) {
        setAppliedCoupon({
          code: couponCode.toUpperCase(),
          discount: result.data.discount,
          discountType: result.data.coupon.discountType,
          discountValue: result.data.coupon.discountValue,
        });
        toast.success("Coupon applied successfully!");
        setCouponCode("");
      } else {
        toast.error(result.message || "Failed to apply coupon");
      }
    } catch (error) {
      console.error("Error applying coupon:", error);
      toast.error("An error occurred while applying the coupon");
    } finally {
      setCouponLoading(false);
    }
  };
  // Remove applied coupon
  const handleRemoveCoupon = () => {
    setAppliedCoupon(null);
    toast.success("Coupon removed");
  };

  const handleSubmit = async () => {
    setLoading(true);
    try {
      let response;

      if (isUpdatingOrder && orderId) {
        // Update existing order
        const itemsToUpdate = cart.map((item) => ({
          dishId: item._id,
          quantity: item.quantity || 1,
          price: item.price,
          dishName: item.name,
          addOns: (item.selectedAddOns || []).map((addOn) => ({
            addOnId: addOn.addOnId,
            name: addOn.name,
            price: addOn.price,
            quantity: addOn.quantity,
          })),
        }));

        response = await fetch(
          `${process.env.NEXT_PUBLIC_BASE_URL}/api/v1/user/orders/${orderId}/update-items`,
          {
            method: "PUT",
            headers: {
              "Content-Type": "application/json",
              Authorization: `Bearer ${localStorage.getItem("user-token")}`,
            },
            body: JSON.stringify({
              items: itemsToUpdate,
              couponCode: appliedCoupon?.code || undefined,
              couponDiscount: appliedCoupon?.discount || 0,
              appliedOffers: appliedOffers.map((offer) => ({
                _id: offer._id,
                offerId: offer.offerId,
                offerName: offer.name,
                offerType: offer.offerType,
                discount: 0, // Will be calculated by backend
              })),
            }),
          }
        );
      } else {
        // Create new order
        response = await fetch(
          `${process.env.NEXT_PUBLIC_BASE_URL}/api/v1/user/create-order`,
          {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
              Authorization: `Bearer ${localStorage.getItem("user-token")}`,
            },
            body: JSON.stringify({
              ...formData,
              items: cart,
              total: subtotal,
              outletId: params?.get("outletId") || "",
              couponCode: appliedCoupon?.code || null,
              couponDiscount: appliedCoupon?.discount || 0,
              finalAmount: total,
              discountType: appliedCoupon?.discountType || "fixed",
              discountValue: appliedCoupon?.discountValue || 0,
              appliedOffers: appliedOffers.map((offer) => ({
                _id: offer._id,
                offerId: offer.offerId,
                offerName: offer.name,
                offerType: offer.offerType,
                discount: 0, // Will be calculated by backend
              })),
            }),
          }
        );
      }

      const order = await response.json();

      if (!order.success) {
        throw new Error(
          order.message ||
            (isUpdatingOrder
              ? "Failed to update order"
              : "Failed to place order")
        );
      }

      // If order was created successfully and we have a coupon, increment its usage
      if (!isUpdatingOrder && appliedCoupon) {
        try {
          await fetch(
            `${process.env.NEXT_PUBLIC_BASE_URL}/api/v1/user/coupons/apply`,
            {
              method: "POST",
              headers: {
                "Content-Type": "application/json",
                Authorization: `Bearer ${localStorage.getItem("user-token")}`,
              },
              body: JSON.stringify({
                code: appliedCoupon.code,
                orderId: order.data._id,
              }),
            }
          );
        } catch (couponError) {
          console.error("Error applying coupon to order:", couponError);
          // Continue with order process even if coupon application fails
        }
      }

      toast.success(
        isUpdatingOrder
          ? "Order updated successfully!"
          : "Order placed successfully!"
      );

      // Emit order event
      const orderIdToTrack = isUpdatingOrder ? orderId : order.data._id;
      customerSocket?.emit("join-order", orderIdToTrack);

      clearCart(); // This now handles removing the cart from localStorage with the proper key
      localStorage.setItem("to-chat", "true");
      setPageLoading(true);
      router.push(`/order-tracking/${orderIdToTrack}`);
    } catch (error) {
      console.error("Order submission failed:", error);
      toast.error("Failed to place order. Please try again.");
    } finally {
      setLoading(false);
    }
  };

  if (pageLoading) {
    return (
      <div className="h-screen flex items-center justify-center">
        <div className="animate-spin">
          <Loader2 />
        </div>
      </div>
    );
  }

  if (cart.length === 0) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <ShoppingBag className="mx-auto mb-4 h-12 w-12 text-gray-400" />
          <h2 className="text-xl font-semibold mb-2">Your cart is empty</h2>
          <p className="text-gray-500 mb-4">
            Add some items to proceed with checkout
          </p>
          <Button onClick={() => router.back()}>Continue Shopping</Button>
        </div>
      </div>
    );
  }

  return (
    <div
      className="min-h-screen bg-blue-200"
      style={{
        backgroundColor: "#f8f9fa",
        overflow: "auto",
        WebkitOverflowScrolling: "touch",
        height: "auto",
        minHeight: "100vh",
      }}
    >
      <div
        className="py-4 px-4 shadow-sm sticky top-0 z-10"
        style={{ backgroundColor: theme.primaryColor }}
      >
        <div className="container mx-auto max-w-6xl flex items-center">
          <Button
            variant="ghost"
            className="text-white hover:bg-white/20"
            onClick={() => router.back()}
          >
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back
          </Button>
          <h1 className="text-xl font-bold text-white ml-4">
            {isUpdatingOrder ? "Update Order" : "Checkout"}
          </h1>
        </div>
      </div>
      <div className="container mx-auto max-w-3xl">
        <div className="space-y-6">
          {/* Main Content */}
          <div className="space-y-6">
            {/* Order Progress */}
            {/* <div className="bg-white rounded-lg shadow-md p-4 mb-6">
              <div className="flex justify-between items-center mb-4">
                <h2 className="text-lg font-semibold">Order Progress</h2>
                <Badge
                  className="px-3 py-1 text-white"
                  style={{ backgroundColor: theme.primaryColor }}
                >
                  Step 1 of 2
                </Badge>
              </div>
              <div className="flex items-center">
                <div className="flex flex-col items-center">
                  <div
                    className="w-10 h-10 rounded-full flex items-center justify-center text-white shadow-sm"
                    style={{ backgroundColor: theme.primaryColor }}
                  >
                    <CheckCircle2 size={20} />
                  </div>
                  <span className="text-xs mt-1 font-medium">Cart</span>
                </div>
                <div
                  className="flex-1 h-1 mx-2"
                  style={{ backgroundColor: theme.primaryColor }}
                ></div>
                <div className="flex flex-col items-center">
                  <div
                    className="w-10 h-10 rounded-full flex items-center justify-center text-white shadow-sm"
                    style={{ backgroundColor: theme.primaryColor }}
                  >
                    <Clock size={20} />
                  </div>
                  <span className="text-xs mt-1 font-medium">Checkout</span>
                </div>
                <div className="flex-1 h-1 mx-2 bg-gray-200"></div>
                <div className="flex flex-col items-center">
                  <div className="w-10 h-10 rounded-full flex items-center justify-center bg-gray-200 text-gray-400 shadow-sm">
                    <CheckCircle2 size={20} />
                  </div>
                  <span className="text-xs mt-1 text-gray-500">Complete</span>
                </div>
              </div>
            </div> */}

            {/* Quick Order Form */}
            {/* <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <User className="mr-2 h-5 w-5" />
                  Quick Order Details
                </CardTitle>
                <CardDescription>
                  Enter essential information to complete your order
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <div className="text-sm font-medium">
                      Name: {formData.name}
                    </div>
                  </div>
                  <div className="space-y-2">
                    <div className="text-sm font-medium">
                      Phone Number: {formData.phone}
                    </div>
                  </div>
                </div>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <div className="text-sm font-medium">
                      Email: {formData.email}
                    </div>
                  </div>
                  <div className="space-y-2">
                    <div className="text-sm font-medium">
                      Table Number: {formData.tableNumber}
                    </div>
                  </div>
                </div>
                <div className="space-y-2">
                  <div className="text-sm font-medium">
                    Special Instructions: {formData.specialInstructions || "None"}
                  </div>
                </div>
              </CardContent>
            </Card> */}

            {/* Payment Method */}
            {/* <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <CreditCard className="mr-2 h-5 w-5" />
                  Payment Method
                </CardTitle>
                <CardDescription>
                  Choose how you&apos;d like to pay
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
                  <div
                    className={`border rounded-lg p-4 cursor-pointer transition-all ${
                      formData.paymentMethod === "cash" ? "border-2" : "border"
                    }`}
                    style={{
                      borderColor:
                        formData.paymentMethod === "cash"
                          ? theme.primaryColor
                          : "",
                    }}
                    onClick={() =>
                      setFormData((prev) => ({
                        ...prev,
                        paymentMethod: "cash",
                      }))
                    }
                  >
                    <div className="flex items-center mb-2">
                      <div
                        className="w-5 h-5 rounded-full border-2 mr-2 flex items-center justify-center"
                        style={{ borderColor: theme.primaryColor }}
                      >
                        {formData.paymentMethod === "cash" && (
                          <div
                            className="w-3 h-3 rounded-full"
                            style={{ backgroundColor: theme.primaryColor }}
                          ></div>
                        )}
                      </div>
                      <span className="font-medium">Cash</span>
                    </div>
                    <div className="flex items-center text-gray-600">
                      <Banknote className="h-4 w-4 mr-2" />
                      <span className="text-sm">Pay on delivery</span>
                    </div>
                  </div>

                  <div
                    className={`border rounded-lg p-4 cursor-pointer transition-all ${
                      formData.paymentMethod === "card" ? "border-2" : "border"
                    }`}
                    style={{
                      borderColor:
                        formData.paymentMethod === "card"
                          ? theme.primaryColor
                          : "",
                    }}
                    onClick={() =>
                      setFormData((prev) => ({
                        ...prev,
                        paymentMethod: "card",
                      }))
                    }
                  >
                    <div className="flex items-center mb-2">
                      <div
                        className="w-5 h-5 rounded-full border-2 mr-2 flex items-center justify-center"
                        style={{ borderColor: theme.primaryColor }}
                      >
                        {formData.paymentMethod === "card" && (
                          <div
                            className="w-3 h-3 rounded-full"
                            style={{ backgroundColor: theme.primaryColor }}
                          ></div>
                        )}
                      </div>
                      <span className="font-medium">Card</span>
                    </div>
                    <div className="flex items-center text-gray-600">
                      <CreditCard className="h-4 w-4 mr-2" />
                      <span className="text-sm">Credit/Debit</span>
                    </div>
                  </div>

                  <div
                    className={`border rounded-lg p-4 cursor-pointer transition-all ${
                      formData.paymentMethod === "upi" ? "border-2" : "border"
                    }`}
                    style={{
                      borderColor:
                        formData.paymentMethod === "upi"
                          ? theme.primaryColor
                          : "",
                    }}
                    onClick={() =>
                      setFormData((prev) => ({ ...prev, paymentMethod: "upi" }))
                    }
                  >
                    <div className="flex items-center mb-2">
                      <div
                        className="w-5 h-5 rounded-full border-2 mr-2 flex items-center justify-center"
                        style={{ borderColor: theme.primaryColor }}
                      >
                        {formData.paymentMethod === "upi" && (
                          <div
                            className="w-3 h-3 rounded-full"
                            style={{ backgroundColor: theme.primaryColor }}
                          ></div>
                        )}
                      </div>
                      <span className="font-medium">UPI</span>
                    </div>
                    <div className="flex items-center text-gray-600">
                      <Smartphone className="h-4 w-4 mr-2" />
                      <span className="text-sm">Google Pay/PhonePe</span>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card> */}

            {/* Applicable Offers - Temporarily disabled */}
            {/* <ApplicableOffers
              orderData={orderData}
              onOfferApply={handleOfferApply}
              appliedOffers={appliedOfferIds}
              className="mb-6"
            /> */}

            <Card className="shadow-xl border-0 rounded-3xl overflow-hidden bg-white">
              {/* Enhanced Header */}
              <div className="relative overflow-hidden">
                <div
                  className="bg-gradient-to-br p-6 relative"
                  style={{
                    background: `linear-gradient(135deg, ${theme.primaryColor}15, ${theme.primaryColor}25, ${theme.primaryColor}10)`,
                  }}
                >
                  {/* Decorative elements */}
                  <div className="absolute top-0 right-0 w-32 h-32 bg-gradient-to-br from-white/20 to-transparent rounded-full -translate-y-16 translate-x-16" />
                  <div className="absolute bottom-0 left-0 w-24 h-24 bg-gradient-to-tr from-white/10 to-transparent rounded-full translate-y-12 -translate-x-12" />

                  <div className="relative z-10">
                    <CardTitle className="flex items-center gap-3 mb-2">
                      <div
                        className="p-3 rounded-2xl shadow-lg"
                        style={{ backgroundColor: theme.primaryColor }}
                      >
                        <ShoppingBag className="h-6 w-6 text-white" />
                      </div>
                      <div>
                        <h2 className="text-2xl font-bold text-gray-900">
                          Order Summary
                        </h2>
                        <div className="flex items-center gap-2 mt-1">
                          <div
                            className="px-3 py-1 rounded-full text-sm font-semibold text-white"
                            style={{ backgroundColor: theme.primaryColor }}
                          >
                            {cart.reduce(
                              (sum, item) => sum + (item.quantity || 1),
                              0
                            )}{" "}
                            items
                          </div>
                          <span className="text-gray-600 text-sm">
                            in your order
                          </span>
                        </div>
                      </div>
                    </CardTitle>
                  </div>
                </div>
              </div>

              <CardContent className="p-0">
                {/* Enhanced Order Items */}
                <div className="max-h-[300px] overflow-y-auto p-6 border-b border-gray-100">
                  <div className="space-y-4">
                    {cart.map((item, index) => (
                      <div
                        key={index}
                        className="group bg-gray-50 rounded-2xl p-4 hover:bg-gray-100 transition-all duration-300 hover:scale-[1.01] border border-gray-100"
                      >
                        <div className="flex justify-between items-start gap-4">
                          <div className="flex-1 min-w-0">
                            <div className="flex items-center gap-3 mb-2">
                              <h4 className="font-semibold text-gray-900 text-base">
                                {item.name}
                              </h4>
                              <Badge
                                variant="outline"
                                className="font-bold border-2"
                                style={{
                                  borderColor: theme.primaryColor,
                                  color: theme.primaryColor,
                                  backgroundColor: `${theme.primaryColor}10`,
                                }}
                              >
                                ×{item.quantity || 1}
                              </Badge>
                            </div>

                            <div className="text-sm text-gray-600 mb-2">
                              ₹{item.price.toFixed(2)} each
                            </div>

                            {/* Enhanced Add-ons Display */}
                            {item.selectedAddOns &&
                              item.selectedAddOns.length > 0 && (
                                <div className="mt-3 p-3 bg-white rounded-xl border border-gray-100">
                                  <div className="text-xs font-semibold text-gray-700 mb-2 flex items-center gap-1">
                                    <span className="text-green-600">🍽️</span>
                                    Add-ons:
                                  </div>
                                  <div className="space-y-1">
                                    {item.selectedAddOns.map(
                                      (addOn, addOnIndex) => (
                                        <div
                                          key={addOnIndex}
                                          className="flex justify-between items-center text-xs bg-gray-50 rounded-lg p-2"
                                        >
                                          <span className="text-gray-700">
                                            • {addOn.name} x{addOn.quantity}
                                          </span>
                                          <span className="font-medium text-gray-900">
                                            +₹
                                            {(
                                              addOn.price * addOn.quantity
                                            ).toFixed(2)}
                                          </span>
                                        </div>
                                      )
                                    )}
                                  </div>
                                </div>
                              )}

                            {/* Enhanced Edit Add-ons Button */}
                            {item.enabledAddOns &&
                              item.enabledAddOns.length > 0 && (
                                <Button
                                  variant="outline"
                                  size="sm"
                                  className="mt-3 text-xs rounded-xl border-2 hover:scale-105 transition-all duration-200"
                                  style={{
                                    borderColor: `${theme.primaryColor}40`,
                                    color: theme.primaryColor,
                                  }}
                                  onClick={() => handleEditAddons(item)}
                                >
                                  <Edit3 className="w-3 h-3 mr-1" />
                                  {item.selectedAddOns &&
                                  item.selectedAddOns.length > 0
                                    ? "Edit Add-ons"
                                    : "Add Add-ons"}
                                </Button>
                              )}
                          </div>

                          <div className="text-right">
                            <div className="font-bold text-lg text-gray-900">
                              ₹{(
                                item.price * (item.quantity || 1) +
                                (item.selectedAddOns || []).reduce(
                                  (sum, addOn) => sum + addOn.price * addOn.quantity,
                                  0
                                )
                              ).toFixed(2)}
                            </div>
                            <div className="text-xs text-gray-500">
                              total price
                            </div>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>

                {/* Enhanced Table Number Input */}
                <div className="p-6 border-b border-gray-100">
                  <div className="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-2xl p-4 border border-blue-100">
                    <label
                      htmlFor="tableNumber"
                      className="block text-sm font-semibold text-gray-800 mb-3 flex items-center gap-2"
                    >
                      <span className="text-blue-600">🪑</span>
                      Table Number (Optional)
                    </label>
                    <Input
                      id="tableNumber"
                      placeholder="e.g., Table 5, A-12, etc."
                      value={formData.tableNumber}
                      onChange={(e) =>
                        setFormData({
                          ...formData,
                          tableNumber: e.target.value,
                        })
                      }
                      className="border-2 rounded-xl focus:ring-2 focus:ring-blue-500 bg-white"
                    />
                  </div>
                </div>

                {/* Enhanced Special Instructions */}
                <div className="p-6 border-b border-gray-100">
                  <div className="bg-gradient-to-r from-amber-50 to-orange-50 rounded-2xl p-4 border border-amber-100">
                    <label
                      htmlFor="specialInstructions"
                      className="block text-sm font-semibold text-gray-800 mb-3 flex items-center gap-2"
                    >
                      <span className="text-amber-600">📝</span>
                      Special Instructions (Optional)
                    </label>
                    <textarea
                      id="specialInstructions"
                      placeholder="Let us know about allergies, spice level, or any other preferences..."
                      value={formData.specialInstructions}
                      onChange={(e) =>
                        setFormData({
                          ...formData,
                          specialInstructions: e.target.value,
                        })
                      }
                      className="w-full px-4 py-3 border-2 border-amber-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-amber-500 focus:border-transparent resize-none bg-white transition-all duration-200"
                      rows={3}
                      maxLength={500}
                    />
                    <div className="flex justify-between items-center mt-2">
                      <div className="text-xs text-amber-600">
                        Share any dietary restrictions or cooking preferences
                      </div>
                      <div className="text-xs text-gray-500 bg-white rounded-full px-2 py-1">
                        {formData.specialInstructions.length}/500
                      </div>
                    </div>
                  </div>
                </div>

                {/* Enhanced Coupon Section */}
                <div className="p-6 border-b border-gray-100">
                  <div className="bg-gradient-to-r from-green-50 to-emerald-50 rounded-2xl p-4 border border-green-100">
                    <div className="flex items-center gap-2 mb-3">
                      <span className="text-green-600 text-lg">🎫</span>
                      <h3 className="text-sm font-semibold text-gray-800">
                        Have a Coupon?
                      </h3>
                    </div>

                    <div className="flex items-center gap-3">
                      <div className="relative flex-1">
                        <Tag className="absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                        <Input
                          placeholder="Enter coupon code (e.g., SAVE20)"
                          className="pl-12 border-2 rounded-xl focus:ring-2 focus:ring-green-500 bg-white"
                          value={couponCode}
                          onChange={(e) => setCouponCode(e.target.value)}
                          disabled={!!appliedCoupon || couponLoading}
                        />
                      </div>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={handleApplyCoupon}
                        disabled={
                          !couponCode || couponLoading || !!appliedCoupon
                        }
                        className="whitespace-nowrap rounded-xl border-2 border-green-300 text-green-700 hover:bg-green-100 px-6 py-2 font-semibold transition-all duration-200 hover:scale-105"
                      >
                        {couponLoading ? (
                          <>
                            <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                            Applying...
                          </>
                        ) : (
                          "Apply"
                        )}
                      </Button>
                    </div>

                    {appliedCoupon && (
                      <div className="mt-4 bg-green-100 p-4 rounded-xl border border-green-200 flex justify-between items-center">
                        <div className="flex items-center gap-2">
                          <span className="text-green-600 text-lg">🎉</span>
                          <div>
                            <span className="text-green-800 font-bold text-sm">
                              {appliedCoupon.code}
                            </span>
                            <div className="text-green-600 text-xs">
                              {appliedCoupon.discountType === "percentage"
                                ? `${appliedCoupon.discountValue}% off applied`
                                : `₹${appliedCoupon.discountValue} off applied`}
                            </div>
                          </div>
                        </div>
                        <Button
                          variant="ghost"
                          size="sm"
                          className="h-8 w-8 p-0 text-gray-500 hover:text-red-500 hover:bg-red-50 rounded-full transition-all duration-200"
                          onClick={handleRemoveCoupon}
                        >
                          <X className="h-4 w-4" />
                        </Button>
                      </div>
                    )}
                  </div>
                </div>

                {/* Enhanced Bill Breakdown */}
                <div className="p-6 space-y-4">
                  <div
                    className="rounded-2xl p-5 border-2"
                    style={{
                      background: `linear-gradient(135deg, ${theme.primaryColor}08, ${theme.primaryColor}12)`,
                      borderColor: `${theme.primaryColor}20`,
                    }}
                  >
                    <h3 className="font-bold text-gray-800 mb-4 flex items-center gap-2">
                      <span className="text-lg">💰</span>
                      Bill Details
                    </h3>

                    <div className="space-y-3">
                      <div className="flex justify-between items-center text-base">
                        <span className="text-gray-700">Subtotal</span>
                        <span className="font-medium">
                          ₹{subtotal.toFixed(2)}
                        </span>
                      </div>

                      {/* Enhanced Applied Offers */}
                      {appliedOffers.length > 0 && (
                        <div className="space-y-2 pt-2 border-t border-gray-200">
                          {appliedOffers.map((offer, index) => (
                            <div
                              key={index}
                              className="flex justify-between items-center bg-green-100 rounded-xl p-3 border border-green-200"
                            >
                              <div className="flex items-center gap-2">
                                <span className="text-green-600">🎁</span>
                                <span className="text-green-800 font-medium text-sm">
                                  {offer.offerName}
                                </span>
                              </div>
                              <span className="text-green-600 font-bold">
                                {"discountAmount" in offer &&
                                typeof offer.discountAmount === "number"
                                  ? `-₹${offer.discountAmount.toFixed(2)}`
                                  : "Applied"}
                              </span>
                            </div>
                          ))}

                          {offerDiscount > 0 && (
                            <div className="flex justify-between items-center font-semibold bg-green-50 rounded-xl p-3">
                              <span className="text-green-700">
                                Total Offer Savings
                              </span>
                              <span className="text-green-600">
                                -₹{offerDiscount.toFixed(2)}
                              </span>
                            </div>
                          )}
                        </div>
                      )}

                      {appliedCoupon && (
                        <div className="flex justify-between items-center bg-blue-100 rounded-xl p-3 border border-blue-200">
                          <div className="flex items-center gap-2">
                            <span className="text-blue-600">🎫</span>
                            <span className="text-blue-800 font-medium">
                              Coupon Discount
                            </span>
                          </div>
                          <span className="text-blue-600 font-bold">
                            -₹{appliedCoupon.discount.toFixed(2)}
                          </span>
                        </div>
                      )}

                      <Separator className="my-4" />

                      <div className="flex justify-between items-center font-bold text-xl pt-2">
                        <span className="text-gray-900">Total Amount</span>
                        <span
                          className="text-2xl"
                          style={{ color: theme.primaryColor }}
                        >
                          ₹{total.toFixed(2)}
                        </span>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Enhanced Place Order Section */}
                <div className="p-6 pt-2">
                  <Button
                    className="w-full py-4 text-lg font-bold shadow-xl transition-all duration-300 hover:scale-[1.02] active:scale-[0.98] rounded-2xl relative overflow-hidden"
                    style={{ backgroundColor: theme.primaryColor }}
                    onClick={handleSubmit}
                    disabled={loading}
                  >
                    {/* Button shimmer effect */}
                    <div className="absolute inset-0 bg-white/20 translate-x-[-100%] group-hover:translate-x-[100%] transition-transform duration-700 skew-x-12" />

                    <div className="relative z-10 flex items-center justify-center gap-3">
                      {loading ? (
                        <>
                          <Loader2 className="h-5 w-5 animate-spin" />
                          <span>Processing Order...</span>
                        </>
                      ) : (
                        <>
                          <span>
                            {isUpdatingOrder
                              ? "Update Order"
                              : "Place Order"}
                          </span>
                        </>
                      )}
                    </div>
                  </Button>

                  {/* Enhanced Profile Warning */}
                  {(!formData.name || !formData.phone) && (
                    <div className="mt-4 bg-amber-50 border-2 border-amber-200 rounded-2xl p-4 flex items-center gap-3">
                      <div className="flex-shrink-0">
                        <AlertCircle className="h-5 w-5 text-amber-600" />
                      </div>
                      <div>
                        <div className="text-amber-800 font-medium text-sm">
                          Profile Incomplete
                        </div>
                        <div className="text-amber-600 text-xs mt-1">
                          Order will use default information. Update your
                          profile for personalized service.
                        </div>
                      </div>
                    </div>
                  )}

                  {/* Enhanced Terms */}
                  <div className="mt-6 text-center">
                    <div className="inline-flex items-center gap-2 bg-gray-50 rounded-full px-4 py-2 border border-gray-100">
                      <span className="text-xs">🔒</span>
                      <span className="text-xs text-gray-600">
                        Secure checkout • Terms & Privacy Policy apply
                      </span>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>

      {/* Addon Selection Dialog */}
      <Dialog
        open={isAddonDialogOpen}
        onOpenChange={(open) => {
          if (!open) {
            setIsAddonDialogOpen(false);
            setEditingItem(null);
            setSelectedAddOns([]);
          }
        }}
      >
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle>
              {(editingItem?.selectedAddOns?.length || 0) > 0
                ? "Edit Add-ons"
                : "Add Add-ons"}{" "}
              for {editingItem?.name}
            </DialogTitle>
          </DialogHeader>
          <div className="space-y-4">
            {editingItem?.enabledAddOns?.map(
              (addon: { _id: string; name: string; price: number }) => {
                const isSelected = selectedAddOns.some(
                  (sa) => sa.addOnId === addon._id
                );
                const selectedAddon = selectedAddOns.find(
                  (sa) => sa.addOnId === addon._id
                );

                return (
                  <div
                    key={addon._id}
                    className="flex items-center justify-between p-3 border rounded-lg"
                  >
                    <div className="flex items-center space-x-3">
                      <Checkbox
                        checked={isSelected}
                        onCheckedChange={(checked) => {
                          if (checked) {
                            setSelectedAddOns([
                              ...selectedAddOns,
                              {
                                addOnId: addon._id,
                                name: addon.name,
                                price: addon.price,
                                quantity: 1,
                              },
                            ]);
                          } else {
                            setSelectedAddOns(
                              selectedAddOns.filter(
                                (sa) => sa.addOnId !== addon._id
                              )
                            );
                          }
                        }}
                      />
                      <div>
                        <div className="font-medium">{addon.name}</div>
                        <div className="text-sm text-gray-500">
                          ₹{addon.price}
                        </div>
                      </div>
                    </div>
                    {isSelected && (
                      <div className="flex items-center space-x-2">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => {
                            const updated = selectedAddOns.map((sa) =>
                              sa.addOnId === addon._id
                                ? {
                                    ...sa,
                                    quantity: Math.max(1, sa.quantity - 1),
                                  }
                                : sa
                            );
                            setSelectedAddOns(updated);
                          }}
                        >
                          -
                        </Button>
                        <span className="w-8 text-center">
                          {selectedAddon?.quantity || 1}
                        </span>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => {
                            const updated = selectedAddOns.map((sa) =>
                              sa.addOnId === addon._id
                                ? { ...sa, quantity: sa.quantity + 1 }
                                : sa
                            );
                            setSelectedAddOns(updated);
                          }}
                        >
                          +
                        </Button>
                      </div>
                    )}
                  </div>
                );
              }
            )}
            <div className="flex space-x-2 pt-4">
              <Button
                variant="outline"
                className="flex-1"
                onClick={() => {
                  setIsAddonDialogOpen(false);
                  setEditingItem(null);
                  setSelectedAddOns([]);
                }}
              >
                Cancel
              </Button>
              <Button
                className="flex-1"
                onClick={handleSaveAddons}
                style={{ backgroundColor: theme.primaryColor }}
              >
                Save Add-ons
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default CheckoutPageWrapper;
