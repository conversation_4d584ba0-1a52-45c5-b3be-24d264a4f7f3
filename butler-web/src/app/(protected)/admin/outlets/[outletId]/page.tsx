"use client";

import { useEffect, useRef, useState } from "react";
import { use<PERSON>ara<PERSON>, useRouter, useSearchParams } from "next/navigation";
import OutletQrCode from "@/components/custom/outlet/OutletQrCode";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import {
  MapPin,
  Phone,
  Calendar,
  Store,
  ChevronLeft,
  IndianRupee,
  Star,
} from "lucide-react";
import { getSingleOutlet, updateDish, getAllDishes } from "@/server/admin";
import { Outlet, Dish } from "@/app/type";
import { Button } from "@/components/ui/button";
import Image from "next/image";
import { Icon } from "@iconify/react/dist/iconify.js";
import CreateDishesDrawer from "@/components/custom/dishes/CreateDishesDrawer";
import DishOutletManagement from "@/components/custom/dishes/DishOutletManagement";
import DishAvailabilitySettings from "@/components/custom/dishes/DishAvailabilitySettings";
import DishIngredientsDialog from "@/components/custom/dishes/DishIngredientsDialog";
import { Switch } from "@/components/ui/switch";
import { toast } from "sonner";
// import { Input } from "@/components/ui/input";

const quotes: string[] = [
  `Welcome to {outlet.name}`,
  `Point camera here. Ask AI about our food.`,
  `QR code → Chat → Perfect meal suggestions.`,
  `Not sure what to eat? AI knows our menu better than anyone.`,
];

const OutletViewPage = () => {
  const router = useRouter();
  const params = useSearchParams();
  const isViewPage = params ? params.get("view") === "true" : false;
  const { outletId } = useParams() || {};
  const [outlet, setOutlet] = useState<Outlet | null>(null);
  const [loading, setLoading] = useState(true);
  const qrCodeRef = useRef(null);
  const [selectedQuote, setSelectedQuote] = useState("");

  // Dish management states
  const [allDishes, setAllDishes] = useState<Dish[]>([]);
  const [loadingDishes, setLoadingDishes] = useState(true);
  const [dishToEdit, setDishToEdit] = useState<Dish | false>(false);
  const [dishForOutletManagement, setDishForOutletManagement] = useState<Dish | null>(null);
  const [dishForAvailability, setDishForAvailability] = useState<Dish | null>(null);
  const [dishForIngredients, setDishForIngredients] = useState<Dish | null>(null);
  const [showCreateDish, setShowCreateDish] = useState(false);

  useEffect(() => {
    // Fetch outlet data and all dishes
    const fetchOutletData = async () => {
      try {
        const res = await getSingleOutlet(outletId as string);
        setOutlet(res.data);
        setLoading(false);
      } catch (error) {
        console.error("Error fetching outlet data:", error);
        setLoading(false);
      }
    };

    const fetchAllDishes = async () => {
      try {
        setLoadingDishes(true);
        const res = await getAllDishes();
        if (res.success) {
          setAllDishes(res.data);
        }
        setLoadingDishes(false);
      } catch (error) {
        console.error("Error fetching all dishes:", error);
        setLoadingDishes(false);
      }
    };

    if (outletId) {
      fetchOutletData();
      fetchAllDishes();
    }
  }, [outletId]);

  // Dish management functions
  const refreshOutletData = async () => {
    try {
      const res = await getSingleOutlet(outletId as string);
      setOutlet(res.data);
      // Also refresh the dishes list to get updated outlet associations
      await fetchAllDishes();
    } catch (error) {
      console.error("Error refreshing outlet data:", error);
    }
  };

  const handleToggleDishAvailability = async (dish: Dish) => {
    try {
      // Check if dish is currently available for this outlet
      const isCurrentlyAvailable = isDishAvailableInOutlet(dish);

      // Update the dish's outlet availability
      const currentOutlets = dish.outlets || [];
      const updatedOutlets = isCurrentlyAvailable
        ? currentOutlets.filter(id => id !== outletId) // Remove outlet
        : [...currentOutlets, outletId as string]; // Add outlet

      const updatedDish = {
        ...dish,
        outlets: updatedOutlets
      };

      const response = await updateDish(updatedDish);

      if (response.success) {
        toast.success(`Dish ${isCurrentlyAvailable ? 'disabled' : 'enabled'} for this outlet successfully`);
        // Refresh both outlet data and all dishes
        refreshOutletData();
        fetchAllDishes();
      } else {
        toast.error("Failed to update dish availability");
      }
    } catch (error) {
      console.error("Error updating dish availability:", error);
      toast.error("Failed to update dish availability");
    }
  };

  // Helper function to check if dish is available in current outlet
  const isDishAvailableInOutlet = (dish: Dish) => {
    return dish.outlets?.includes(outletId as string) || false;
  };

  // Separate function to fetch all dishes (for reuse)
  const fetchAllDishes = async () => {
    try {
      setLoadingDishes(true);
      const res = await getAllDishes();
      if (res.success) {
        setAllDishes(res.data);
      }
      setLoadingDishes(false);
    } catch (error) {
      console.error("Error fetching all dishes:", error);
      setLoadingDishes(false);
    }
  };


  useEffect(() => {
    setSelectedQuote(
      quotes[Math.floor(Math.random() * quotes.length)].replace(
        "{outlet.name}",
        outlet?.name || ""
      )
    );
    const intervalId = setInterval(() => {
      setSelectedQuote(
        quotes[Math.floor(Math.random() * quotes.length)].replace(
          "{outlet.name}",
          outlet?.name || ""
        )
      );
    }, 10000);
    return () => clearInterval(intervalId);
  }, [outlet]);

  const formatDate = (dateString?: string) => {
    if (!dateString) return "N/A";
    return new Date(dateString).toLocaleDateString("en-US", {
      year: "numeric",
      month: "long",
      day: "numeric",
    });
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center min-h-screen">
        <div className="text-lg font-medium">Loading outlet information...</div>
      </div>
    );
  }

  if (!outlet) {
    return (
      <div className="flex justify-center items-center min-h-screen">
        <div className="text-lg font-medium text-red-500">Outlet not found</div>
      </div>
    );
  }

  if (isViewPage) {
    return (
      <div className="absolute inset-0 bg-white min-h-screen flex flex-col items-center justify-center p-6 z-50">
        {/* Download Button */}
        <div className="absolute top-10 right-10">
          <div
            className="flex justify-center gap-2 cursor-pointer"
            // onClick={handleDownload}
            onClick={() => window.print()}
          >
            <Icon icon="line-md:download-loop" width="30" height="30" />
          </div>
        </div>

        {/* Page Content */}
        <div ref={qrCodeRef}>
          {/* Absolute Logo */}
          <Image
            src="/ai_image.png"
            alt="Outlet Logo"
            width={200}
            height={200}
            className="absolute bottom-20 right-10"
          />

          <div className="max-w-md w-full mx-auto text-center space-y-8">
            {/* Header */}
            <div className="space-y-4">
              <h1 className="text-3xl font-bold text-gray-900 gradient-flow">
                {selectedQuote}
              </h1>
              <p className="text-lg text-gray-600">
                Scan the QR code to chat with your personal AI butler
              </p>
            </div>

            {/* QR Code Section */}
            <div className="bg-white p-8 rounded-2xl shadow-lg border-2 border-blue-100 flex flex-col items-center">
              <div className="mb-6">
                <OutletQrCode outletId={String(outletId)} onlyQr />
              </div>
              <p className="text-sm text-gray-500">
                Point your camera at the QR code to start ordering
              </p>
            </div>

            {/* Features Section */}
            <div className="grid grid-cols-2 gap-4 text-center mt-8">
              <div className="p-4 rounded-lg bg-blue-50">
                <h3 className="font-semibold text-blue-900">Easy Ordering</h3>
                <p className="text-sm text-blue-700">
                  Order directly from your phone
                </p>
              </div>
              <div className="p-4 rounded-lg bg-blue-50">
                <h3 className="font-semibold text-blue-900">AI Assistant</h3>
                <p className="text-sm text-blue-700">
                  Get personalized recommendations
                </p>
              </div>
            </div>

            {/* Footer */}
            <div className="text-sm text-gray-500 flex justify-center items-center gap-1.5">
              Powered by{" "}
              <span className="font-bold text-blue-700">Butler AI</span>
              <Image
                src={"/logos/butler.png"}
                alt={"Butler"}
                width={20}
                height={20}
              />
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-2 px-1 md:px-4">
      <Button className="mb-3" onClick={() => router.back()}>
        <ChevronLeft /> Back
      </Button>
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <div className="lg:col-span-2">
          <Card className="shadow-md">
            <CardHeader className="border-b pb-4">
              <div className="flex justify-between items-start">
                <div>
                  <CardTitle className="md:text-2xl text-md font-bold">
                    {outlet.name}
                  </CardTitle>
                  <p className="text-gray-500 mt-1">
                    {typeof outlet?.foodChain === "string"
                      ? outlet?.foodChain
                      : outlet?.foodChain?.name}
                  </p>
                </div>
                <Badge
                  variant={outlet.isCloudKitchen ? "default" : "outline"}
                  className={outlet.isCloudKitchen ? "bg-blue-500" : ""}
                >
                  {outlet.isCloudKitchen
                    ? "Cloud Kitchen"
                    : "Physical Location"}
                </Badge>
              </div>
            </CardHeader>
            <CardContent className="md:pt-6">
              <div className="space-y-4">
                <div className="flex items-start">
                  <MapPin className="h-5 w-5 text-gray-500 mr-2 mt-0.5" />
                  <div>
                    <h3 className="font-semibold">Address</h3>
                    <p className="text-gray-600">{outlet.address}</p>
                  </div>
                </div>

                <div className="flex items-start">
                  <Phone className="h-5 w-5 text-gray-500 mr-2 mt-0.5" />
                  <div>
                    <h3 className="font-semibold">Contact</h3>
                    <p className="text-gray-600">{outlet.contact}</p>
                  </div>
                </div>

                <div className="flex items-start">
                  <Store className="h-5 w-5 text-gray-500 mr-2 mt-0.5" />
                  <div>
                    <h3 className="font-semibold">Food Chain</h3>
                    <p className="text-gray-600">
                      {typeof outlet?.foodChain === "string"
                        ? outlet?.foodChain
                        : outlet?.foodChain?.name}
                    </p>
                  </div>
                </div>

                <div className="flex items-start">
                  <Calendar className="h-5 w-5 text-gray-500 mr-2 mt-0.5" />
                  <div>
                    <h3 className="font-semibold">Created</h3>
                    <p className="text-gray-600">
                      {formatDate(outlet.createdAt)}
                    </p>
                    <p className="text-sm text-gray-500 mt-1">
                      Last updated: {formatDate(outlet.updatedAt)}
                    </p>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        <div>
          <Card className="shadow-md">
            <CardHeader>
              <CardTitle className="text-lg font-medium">
                Outlet QR Code
              </CardTitle>
            </CardHeader>
            <CardContent className="flex flex-col items-center">
              <div className="mb-4 p-4 bg-white rounded-lg shadow-sm">
                <OutletQrCode outletId={String(outletId)} />
              </div>
              <p className="text-sm text-gray-500 text-center">
                Scan this QR code to talk with Outlet&apos;s Butler
              </p>
            </CardContent>
          </Card>
        </div>

        <div>
          <Card>
            <CardHeader>
              <h2 className="font-bold">Dishes Management</h2>
              {/* <Input value={'hi'} placeholder="Search dishes..."/> */}
            </CardHeader>
            <CardContent className="flex h-[400px] overflow-y-scroll flex-col">
              {loadingDishes ? (
                <div className="flex justify-center items-center h-full">
                  <div className="text-gray-500">Loading dishes...</div>
                </div>
              ) : allDishes && allDishes.length > 0 ? (
                allDishes.map((dish) => {
                  const isAvailableInOutlet = isDishAvailableInOutlet(dish);
                  return (
                    <div
                      className="m-2 p-3 border rounded-lg flex justify-between items-center gap-3 w-full h-fit hover:shadow-sm transition-shadow"
                      key={dish._id}
                    >
                      <div className="flex items-center gap-3 flex-1">
                        <div className="flex flex-col">
                          <div className="flex items-center gap-2">
                            <span className="font-medium">{dish.name}</span>
                            {dish.isFeatured && (
                              <Star className="h-4 w-4 text-yellow-500 fill-current" />
                            )}
                            {!dish.isAvailable && (
                              <Badge variant="secondary" className="text-xs">
                                Globally Unavailable
                              </Badge>
                            )}
                          </div>
                          <div className="flex items-center gap-2 text-sm text-gray-600">
                            <IndianRupee className="h-3 w-3" />
                            <span>{dish.price}</span>
                            {dish.category && (
                              <>
                                <span>•</span>
                                <span>{typeof dish.category === 'string' ? dish.category : dish.category.name}</span>
                              </>
                            )}
                          </div>
                        </div>
                      </div>

                      <div className="flex items-center gap-2">
                        <div className="flex items-center gap-1">

                          <Switch
                            checked={isAvailableInOutlet}
                            disabled={!dish.isAvailable} // Disable if dish is globally unavailable
                            onCheckedChange={() => handleToggleDishAvailability(dish)}
                          />
                        </div>
                      </div>
                    </div>
                  );
                })
              ) : (
                <div className="flex flex-col items-center justify-center h-full text-gray-500">
                  <p>No dishes found in the food chain</p>
                  <Button
                    variant="link"
                    onClick={() => setShowCreateDish(true)}
                  >
                    Create your first dish
                  </Button>
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Dish Management Dialogs */}
      {(showCreateDish || dishToEdit) && (
        <CreateDishesDrawer
          onSuccess={() => {
            refreshOutletData();
            setDishToEdit(false);
            setShowCreateDish(false);
          }}
          isUpdate={dishToEdit ? true : false}
          dishToEdit={dishToEdit !== false ? dishToEdit : undefined}
          onClose={() => {
            setDishToEdit(false);
            setShowCreateDish(false);
          }}
          preselectedOutletId={outletId as string}
        />
      )}

      {dishForOutletManagement && (
        <DishOutletManagement
          dish={dishForOutletManagement}
          open={!!dishForOutletManagement}
          onOpenChange={(open) => !open && setDishForOutletManagement(null)}
          onSuccess={() => {
            refreshOutletData();
            setDishForOutletManagement(null);
          }}
        />
      )}

      {dishForAvailability && (
        <DishAvailabilitySettings
          dish={dishForAvailability}
          open={!!dishForAvailability}
          onOpenChange={(open) => !open && setDishForAvailability(null)}
          onSuccess={() => {
            refreshOutletData();
            setDishForAvailability(null);
          }}
        />
      )}

      {dishForIngredients && (
        <DishIngredientsDialog
          dish={dishForIngredients}
          open={!!dishForIngredients}
          onClose={(refresh) => {
            if (refresh) refreshOutletData();
            setDishForIngredients(null);
          }}
        />
      )}
    </div>
  );
};

export default OutletViewPage;
