"use client";
import { useTheme } from "@/contexts/ThemeContext";
import { getRoutes } from "@/routes";
import { Toaster } from "sonner";
import NotificationBell from "@/components/notifications/NotificationBell";
import { UserButton } from "@/components/UserButton";
import ResponsiveSidebar from "@/components/layouts/ResponsiveSidebar";
import GlobalOrderNotifications from "@/components/notifications/GlobalOrderNotifications";

export default function AdminLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const routes = getRoutes("admin");
  const { theme } = useTheme();

  return (
    <div className="min-h-screen flex flex-col sm:flex-row">
      {/* Admin sidebar */}
      <ResponsiveSidebar
        routes={routes}
        title={theme.name}
        backgroundColor={theme.primaryColor}
        textColor="#ffffff"
      />

      {/* Main content */}
      <div className="h-screen overflow-y-auto w-full">
        <div className="flex-1 flex flex-col min-h-screen overflow-y-auto">
          <header className="border-b p-4 flex justify-end items-center gap-2 bg-white">
            <NotificationBell />
            <UserButton />
          </header>
          <main className="flex-1 p-4 md:p-8">{children}</main>
        </div>
      </div>
      <Toaster />
      <GlobalOrderNotifications />
    </div>
  );
}
